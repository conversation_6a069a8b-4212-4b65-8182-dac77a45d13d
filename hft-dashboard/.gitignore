# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
*/node_modules/
__pycache__/
*/__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt

# Build outputs
build/
dist/
*.egg-info/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.db
*.sqlite
*.sqlite3
data/
backups/

# Docker
.dockerignore

# MacOS
.DS_Store

# Temporary files
*.tmp
*.temp

# Secret keys and certificates
*.key
*.pem
*.crt
*.cert

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# Local development
.local/
.cache/

# ClickHouse data
clickhouse-config/data/
