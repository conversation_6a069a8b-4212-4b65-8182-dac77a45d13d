# Backend API Testing Guide

## Quick Start

1. Start services: `docker-compose up postgres redis -d`
2. Run migrations: `docker-compose run --rm backend alembic upgrade head`
3. Start backend: `docker-compose up backend -d`
4. Test at: http://localhost:8000/docs

## Test Endpoints

### Register User
```bash
curl -X POST "http://localhost:8000/users/register" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123", "full_name": "Test User"}'
```

### Get Token
```bash
curl -X POST "http://localhost:8000/users/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=password123"
```

### Get Account Info (with token)
```bash
curl -X GET "http://localhost:8000/accounts/me" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## API Documentation
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc 