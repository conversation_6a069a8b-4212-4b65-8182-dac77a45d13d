from app.models.account import Account
from app.models.user import User
from app.schemas.account import AccountCreate, AccountUpdate
from app.db.database import SessionLocal
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

def create_user_account(user_id: int) -> Account:
    """Create a new account for a user with default values"""
    db = SessionLocal()
    account = Account(user_id=user_id)
    db.add(account)
    db.commit()
    db.refresh(account)
    db.close()
    return account

def get_user_account(user_id: int) -> Account:
    """Get user's account"""
    db = SessionLocal()
    stmt = select(Account).where(Account.user_id == user_id)
    account = db.execute(stmt).scalar_one_or_none()
    db.close()
    return account

def update_account(user_id: int, account_update: AccountUpdate) -> Account:
    """Update user's account"""
    db = SessionLocal()
    stmt = select(Account).where(Account.user_id == user_id)
    account = db.execute(stmt).scalar_one_or_none()
    
    if not account:
        db.close()
        return None
    
    update_data = account_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(account, field, value)
    
    db.commit()
    db.refresh(account)
    db.close()
    return account

def update_daily_pnl(user_id: int, pnl_change: float) -> Account:
    """Update daily P&L and check if trading should be blocked"""
    db = SessionLocal()
    stmt = select(Account).where(Account.user_id == user_id)
    account = db.execute(stmt).scalar_one_or_none()
    
    if not account:
        db.close()
        return None
    
    account.daily_pnl += pnl_change
    
    # Check if daily loss limit is exceeded
    if account.check_daily_loss_limit():
        account.is_trading_blocked = True
    
    db.commit()
    db.refresh(account)
    db.close()
    return account

def reset_daily_pnl(user_id: int) -> Account:
    """Reset daily P&L (called at start of new trading day)"""
    db = SessionLocal()
    stmt = select(Account).where(Account.user_id == user_id)
    account = db.execute(stmt).scalar_one_or_none()
    
    if not account:
        db.close()
        return None
    
    account.daily_pnl = 0.0
    account.is_trading_blocked = False
    
    db.commit()
    db.refresh(account)
    db.close()
    return account

def can_user_trade(user_id: int) -> bool:
    """Check if user can place trades"""
    account = get_user_account(user_id)
    return account.can_trade() if account else False 