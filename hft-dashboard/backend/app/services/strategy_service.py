from typing import List, Optional
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from app.db.database import SessionLocal
from app.models.strategy import Strategy
from app.schemas.strategy import StrategyCreate, StrategyUpdate

async def create_strategy(strategy_data: StrategyCreate, user_id: int) -> Strategy:
    """Create a new strategy"""
    async with SessionLocal() as db:
        new_strategy = Strategy(
            name=strategy_data.name,
            description=strategy_data.description,
            type=strategy_data.type,
            symbols=strategy_data.symbols,
            parameters=strategy_data.parameters,
            user_id=user_id
        )
        
        db.add(new_strategy)
        await db.commit()
        await db.refresh(new_strategy)
        return new_strategy

async def get_user_strategies(user_id: int) -> List[Strategy]:
    """Get all strategies for a user"""
    async with SessionLocal() as db:
        stmt = select(Strategy).where(Strategy.user_id == user_id).order_by(Strategy.created_at.desc())
        result = await db.execute(stmt)
        return result.scalars().all()

async def get_strategy(strategy_id: int, user_id: int) -> Optional[Strategy]:
    """Get a specific strategy by ID and user"""
    async with SessionLocal() as db:
        stmt = select(Strategy).where(
            Strategy.id == strategy_id,
            Strategy.user_id == user_id
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

async def update_strategy(strategy_id: int, strategy_update: StrategyUpdate, user_id: int) -> Optional[Strategy]:
    """Update a strategy"""
    async with SessionLocal() as db:
        stmt = select(Strategy).where(
            Strategy.id == strategy_id,
            Strategy.user_id == user_id
        )
        result = await db.execute(stmt)
        strategy = result.scalar_one_or_none()
        
        if not strategy:
            return None
        
        # Update fields
        update_data = strategy_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(strategy, field, value)
        
        await db.commit()
        await db.refresh(strategy)
        return strategy

async def delete_strategy(strategy_id: int, user_id: int) -> bool:
    """Delete a strategy"""
    async with SessionLocal() as db:
        stmt = select(Strategy).where(
            Strategy.id == strategy_id,
            Strategy.user_id == user_id
        )
        result = await db.execute(stmt)
        strategy = result.scalar_one_or_none()
        
        if not strategy:
            return False
        
        await db.delete(strategy)
        await db.commit()
        return True

async def get_strategies() -> List[Strategy]:
    """Get all strategies (admin only)"""
    async with SessionLocal() as db:
        stmt = select(Strategy).order_by(Strategy.created_at.desc())
        result = await db.execute(stmt)
        return result.scalars().all()
