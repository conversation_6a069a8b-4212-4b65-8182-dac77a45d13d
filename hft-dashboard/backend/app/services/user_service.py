from app.models.user import User
from app.schemas.user import UserCreate
from app.db.database import SessionLoc<PERSON>
from app.utils.security import get_password_hash, verify_password
from app.services.account_service import create_user_account
from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError
import secrets
from datetime import datetime, timedelta

# Async database operations

async def register_user(user: UserCreate):
    async with SessionLocal() as db:
        db_user = User(email=user.email, hashed_password=get_password_hash(user.password), full_name=user.full_name)
        db.add(db_user)
        try:
            await db.commit()
            await db.refresh(db_user)
            # Create account for the user
            await create_user_account(db_user.id)
            return db_user
        except IntegrityError:
            await db.rollback()
            raise ValueError("Email already registered")

async def authenticate_user(email: str, password: str):
    async with SessionLocal() as db:
        stmt = select(User).where(User.email == email)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        if user and verify_password(password, user.hashed_password):
            return user
        return None

async def create_password_reset_token(email: str) -> str:
    """Create a password reset token for the user"""
    async with SessionLocal() as db:
        stmt = select(User).where(User.email == email)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()

        if not user:
            return None

        # Generate a secure token
        token = secrets.token_urlsafe(32)
        user.reset_token = token
        user.reset_token_expires = datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour

        await db.commit()
        return token

async def reset_password_with_token(token: str, new_password: str) -> bool:
    """Reset password using the provided token"""
    async with SessionLocal() as db:
        stmt = select(User).where(User.reset_token == token)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()

        if not user or not user.reset_token_expires or user.reset_token_expires < datetime.utcnow():
            return False

        # Update password and clear reset token
        user.hashed_password = get_password_hash(new_password)
        user.reset_token = None
        user.reset_token_expires = None

        await db.commit()
        return True
