from typing import List, Optional
from datetime import datetime
import aiohttp
import os
import logging
from app.schemas.market_data import (
    MarketDataOut, OrderBookOut, SymbolOut, 
    HistoricalDataPoint, MarketStatusOut, OrderBookEntryOut
)

logger = logging.getLogger(__name__)

# Market Data Handler service URL
MARKET_DATA_SERVICE_URL = os.getenv("MARKET_DATA_SERVICE_URL", "http://localhost:8002")

async def get_market_data(symbol: str) -> Optional[MarketDataOut]:
    """Get latest market data for a symbol"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{MARKET_DATA_SERVICE_URL}/market-data/{symbol}") as response:
                if response.status == 200:
                    data = await response.json()
                    return MarketDataOut(**data)
                elif response.status == 404:
                    return None
                else:
                    logger.error(f"Market data service error: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"Failed to fetch market data for {symbol}: {e}")
        # Return mock data for development
        return MarketDataOut(
            symbol=symbol,
            last_price=18500.0,
            change=25.50,
            change_percent=0.14,
            volume=1000000,
            high=18550.0,
            low=18450.0,
            open=18475.0,
            timestamp=datetime.now()
        )

async def get_order_book(symbol: str) -> Optional[OrderBookOut]:
    """Get order book for a symbol"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{MARKET_DATA_SERVICE_URL}/market-data/{symbol}/orderbook") as response:
                if response.status == 200:
                    data = await response.json()
                    return OrderBookOut(**data)
                elif response.status == 404:
                    return None
    except Exception as e:
        logger.error(f"Failed to fetch order book for {symbol}: {e}")
        # Return mock data for development
        return OrderBookOut(
            symbol=symbol,
            bids=[
                OrderBookEntryOut(price=18499.0, quantity=100, total=1849900),
                OrderBookEntryOut(price=18498.0, quantity=200, total=3699600),
                OrderBookEntryOut(price=18497.0, quantity=150, total=2774550)
            ],
            asks=[
                OrderBookEntryOut(price=18501.0, quantity=75, total=1387575),
                OrderBookEntryOut(price=18502.0, quantity=125, total=2312750),
                OrderBookEntryOut(price=18503.0, quantity=100, total=1850300)
            ],
            timestamp=datetime.now()
        )

async def get_symbols() -> List[SymbolOut]:
    """Get list of available symbols"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{MARKET_DATA_SERVICE_URL}/symbols") as response:
                if response.status == 200:
                    data = await response.json()
                    return [SymbolOut(**symbol) for symbol in data.get("symbols", [])]
    except Exception as e:
        logger.error(f"Failed to fetch symbols: {e}")
        
    # Return mock data for development
    return [
        SymbolOut(
            symbol="NIFTY",
            name="Nifty 50",
            exchange="NSE",
            instrument_type="INDEX",
            lot_size=1,
            tick_size=0.05
        ),
        SymbolOut(
            symbol="BANKNIFTY",
            name="Bank Nifty",
            exchange="NSE",
            instrument_type="INDEX",
            lot_size=1,
            tick_size=0.05
        )
    ]

async def get_historical_data(symbol: str, start_date: datetime, end_date: datetime) -> List[HistoricalDataPoint]:
    """Get historical data for a symbol"""
    try:
        params = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{MARKET_DATA_SERVICE_URL}/market-data/{symbol}/historical",
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return [HistoricalDataPoint(**point) for point in data.get("data", [])]
    except Exception as e:
        logger.error(f"Failed to fetch historical data for {symbol}: {e}")
    
    # Return mock data for development
    return []

async def get_market_status() -> MarketStatusOut:
    """Get current market status"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{MARKET_DATA_SERVICE_URL}/market-status") as response:
                if response.status == 200:
                    data = await response.json()
                    return MarketStatusOut(**data)
    except Exception as e:
        logger.error(f"Failed to fetch market status: {e}")
    
    # Return mock data for development
    now = datetime.now()
    return MarketStatusOut(
        status="open",
        message="Market is open for trading",
        next_close=now.replace(hour=15, minute=30, second=0, microsecond=0)
    )
