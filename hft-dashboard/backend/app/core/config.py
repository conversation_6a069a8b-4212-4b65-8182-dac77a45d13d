import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "hft_dashboard")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "hft_user")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "secure_password_123")
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST", "postgres")
    POSTGRES_PORT: int = int(os.getenv("POSTGRES_PORT", 5432))
    SECRET_KEY: str = os.getenv("SECRET_KEY", "supersecret")
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "superjwtsecret")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30))

settings = Settings()
