from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Float, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class Account(Base):
    __tablename__ = "accounts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    capital = Column(Float, default=100000.0)  # Initial capital in INR
    daily_loss_limit = Column(Float, default=50000.0)  # Max daily loss in INR
    daily_pnl = Column(Float, default=0.0)  # Current day's P&L
    is_trading_blocked = Column(Boolean, default=False)  # Block trading when limits exceeded
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship
    user = relationship("User", back_populates="account")

    def check_daily_loss_limit(self) -> bool:
        """Check if daily loss limit is exceeded"""
        return self.daily_pnl <= -self.daily_loss_limit

    def can_trade(self) -> bool:
        """Check if user can place trades"""
        return not self.is_trading_blocked and not self.check_daily_loss_limit() 