from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class Strategy(Base):
    __tablename__ = "strategies"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    type = Column(String(50), nullable=False)  # momentum, mean_reversion, arbitrage, custom
    status = Column(String(20), default="inactive")  # active, inactive, paused
    symbols = Column(JSON, nullable=False)  # List of trading symbols
    parameters = Column(JSON, nullable=False)  # Strategy parameters
    
    # Performance metrics
    total_pnl = Column(Float, default=0.0)
    daily_pnl = Column(Float, default=0.0)
    win_rate = Column(Float, default=0.0)
    total_trades = Column(Integer, default=0)
    sharpe_ratio = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    
    # Relationships
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user = relationship("User", back_populates="strategies")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    @property
    def performance(self):
        """Return performance metrics as a dict"""
        return {
            "total_pnl": self.total_pnl,
            "daily_pnl": self.daily_pnl,
            "win_rate": self.win_rate,
            "total_trades": self.total_trades,
            "sharpe_ratio": self.sharpe_ratio,
            "max_drawdown": self.max_drawdown
        }
