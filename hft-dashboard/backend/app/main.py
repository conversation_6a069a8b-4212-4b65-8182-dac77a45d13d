from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import user, account
from app.models import user as user_model, account as account_model

app = FastAPI(title="HFT Dashboard Backend API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health", tags=["Health"])
def health_check():
    return {"status": "ok"}

app.include_router(user.router, prefix="/users", tags=["Users"])
app.include_router(account.router, prefix="/accounts", tags=["Accounts"])
