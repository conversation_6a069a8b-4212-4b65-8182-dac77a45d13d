from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import user, account, strategy, market_data, system
from app.models import user as user_model, account as account_model, strategy as strategy_model
from app.db.database import engine, Base

app = FastAPI(title="HFT Dashboard Backend API")

@app.on_event("startup")
async def startup_event():
    # Create database tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

# Add CORS middleware with enhanced settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["Authorization", "Content-Type", "Accept", "Origin", "X-Requested-With"],
    expose_headers=["Content-Length"],
    max_age=600,  # Cache preflight requests for 10 minutes
)

@app.get("/health", tags=["Health"])
def health_check():
    return {"status": "ok"}

app.include_router(user.router, prefix="/users", tags=["Users"])
app.include_router(account.router, prefix="/accounts", tags=["Accounts"])
app.include_router(strategy.router, prefix="/strategies", tags=["Strategies"])
app.include_router(market_data.router, prefix="/market-data", tags=["Market Data"])
app.include_router(system.router, prefix="/system", tags=["System"])
