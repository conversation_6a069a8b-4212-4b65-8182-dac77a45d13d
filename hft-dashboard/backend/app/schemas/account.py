from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class AccountBase(BaseModel):
    capital: float
    daily_loss_limit: float

class AccountCreate(AccountBase):
    user_id: int

class AccountUpdate(BaseModel):
    capital: Optional[float] = None
    daily_loss_limit: Optional[float] = None
    daily_pnl: Optional[float] = None
    is_trading_blocked: Optional[bool] = None

class AccountOut(AccountBase):
    id: int
    user_id: int
    daily_pnl: float
    is_trading_blocked: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PasswordResetRequest(BaseModel):
    email: str

class PasswordReset(BaseModel):
    token: str
    new_password: str 