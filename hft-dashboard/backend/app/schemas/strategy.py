from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

class StrategyBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    type: str = Field(..., description="Strategy type: momentum, mean_reversion, arbitrage, custom")
    symbols: List[str] = Field(..., description="List of trading symbols")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Strategy parameters")

class StrategyCreate(StrategyBase):
    pass

class StrategyUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    type: Optional[str] = None
    symbols: Optional[List[str]] = None
    parameters: Optional[Dict[str, Any]] = None
    status: Optional[str] = Field(None, description="Strategy status: active, inactive, paused")

class StrategyPerformance(BaseModel):
    total_pnl: float = 0.0
    daily_pnl: float = 0.0
    win_rate: float = 0.0
    total_trades: int = 0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0

class StrategyOut(StrategyBase):
    id: int
    status: str
    performance: StrategyPerformance
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
