from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class MarketDataBase(BaseModel):
    symbol: str
    last_price: float
    change: float
    change_percent: float
    volume: int
    high: float
    low: float
    open: float

class MarketDataOut(MarketDataBase):
    timestamp: datetime

class OrderBookEntryOut(BaseModel):
    price: float
    quantity: int
    total: float

class OrderBookOut(BaseModel):
    symbol: str
    bids: List[OrderBookEntryOut]
    asks: List[OrderBookEntryOut]
    timestamp: datetime

class SymbolOut(BaseModel):
    symbol: str
    name: str
    exchange: str
    instrument_type: str
    lot_size: int
    tick_size: float

class HistoricalDataPoint(BaseModel):
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int

class MarketStatusOut(BaseModel):
    status: str  # open, closed, pre_open, post_close
    message: str
    next_open: Optional[datetime] = None
    next_close: Optional[datetime] = None
