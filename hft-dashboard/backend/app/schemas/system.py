from pydantic import BaseModel
from typing import Dict, Any
from datetime import datetime

class HealthStatusOut(BaseModel):
    status: str  # healthy, warning, critical
    message: str
    last_check: datetime

class SystemMetricsOut(BaseModel):
    latency_ms: float
    memory_usage_percent: float
    cpu_usage_percent: float
    active_connections: int
    disk_usage_percent: float
    network_throughput_mbps: float

class ServiceHealthOut(BaseModel):
    database: HealthStatusOut
    redis: HealthStatusOut
    market_data: HealthStatusOut
    execution_engine: HealthStatusOut
    strategy_engine: HealthStatusOut

class SystemHealthOut(BaseModel):
    status: str  # healthy, warning, critical
    components: ServiceHealthOut
    metrics: SystemMetricsOut
    last_updated: datetime
    uptime: str
    version: str
