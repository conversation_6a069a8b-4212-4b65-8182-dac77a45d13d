from fastapi import APIRouter, Depends, HTTPException, status
from app.schemas.account import AccountOut, AccountUpdate
from app.services.account_service import get_user_account, update_account
from app.auth.auth import get_current_user
from app.models.user import User

router = APIRouter()

@router.get("/me", response_model=AccountOut)
def get_my_account(current_user: User = Depends(get_current_user)):
    """Get current user's account information"""
    account = get_user_account(current_user.id)
    if not account:
        raise HTTPException(status_code=404, detail="Account not found")
    return account

@router.put("/me", response_model=AccountOut)
def update_my_account(
    account_update: AccountUpdate,
    current_user: User = Depends(get_current_user)
):
    """Update current user's account settings"""
    account = update_account(current_user.id, account_update)
    if not account:
        raise HTTPException(status_code=404, detail="Account not found")
    return account

@router.get("/me/can-trade")
def check_can_trade(current_user: User = Depends(get_current_user)):
    """Check if user can place trades"""
    from app.services.account_service import can_user_trade
    can_trade = can_user_trade(current_user.id)
    return {"can_trade": can_trade} 