from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import datetime, timedelta
from app.schemas.market_data import MarketDataOut, OrderBookOut, SymbolOut
from app.services.market_data_service import (
    get_market_data, get_order_book, get_symbols,
    get_historical_data, get_market_status
)
from app.auth.auth import get_current_user
from app.models.user import User
import aiohttp
import os

router = APIRouter()

@router.get("/status")
async def market_status(current_user: User = Depends(get_current_user)):
    """Get market status"""
    try:
        status = await get_market_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch market status")

@router.get("/symbols", response_model=List[SymbolOut])
async def list_symbols(current_user: User = Depends(get_current_user)):
    """Get list of available symbols"""
    try:
        symbols = await get_symbols()
        return symbols
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch symbols")

@router.get("/{symbol}", response_model=MarketDataOut)
async def get_symbol_data(
    symbol: str,
    current_user: User = Depends(get_current_user)
):
    """Get latest market data for a symbol"""
    try:
        market_data = await get_market_data(symbol)
        if not market_data:
            raise HTTPException(status_code=404, detail="Symbol not found")
        return market_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch market data")

@router.get("/{symbol}/orderbook", response_model=OrderBookOut)
async def get_symbol_orderbook(
    symbol: str,
    current_user: User = Depends(get_current_user)
):
    """Get order book for a symbol"""
    try:
        orderbook = await get_order_book(symbol)
        if not orderbook:
            raise HTTPException(status_code=404, detail="Order book not found")
        return orderbook
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch order book")

@router.get("/{symbol}/historical")
async def get_symbol_historical(
    symbol: str,
    days: int = Query(default=30, ge=1, le=365),
    current_user: User = Depends(get_current_user)
):
    """Get historical data for a symbol"""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        historical_data = await get_historical_data(symbol, start_date, end_date)
        return {"symbol": symbol, "data": historical_data, "days": days}
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch historical data")

@router.get("/")
async def market_data_overview(current_user: User = Depends(get_current_user)):
    """Get market data overview"""
    try:
        # Get data for major indices
        major_symbols = ["NIFTY", "BANKNIFTY", "NIFTY50"]
        overview = {}
        
        for symbol in major_symbols:
            try:
                data = await get_market_data(symbol)
                if data:
                    overview[symbol] = data
            except:
                continue
        
        return {"overview": overview, "timestamp": datetime.now().isoformat()}
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch market overview")
