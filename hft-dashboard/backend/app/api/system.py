from fastapi import APIRouter, Depends
from app.schemas.system import SystemHealthOut
from app.services.system_service import get_system_health
from app.auth.auth import get_current_user
from app.models.user import User

router = APIRouter()

@router.get("/health", response_model=SystemHealthOut)
async def system_health(current_user: User = Depends(get_current_user)):
    """Get comprehensive system health status"""
    health_data = await get_system_health()
    return health_data

@router.get("/metrics")
async def system_metrics(current_user: User = Depends(get_current_user)):
    """Get detailed system metrics"""
    # This would integrate with Prometheus or other monitoring systems
    return {
        "cpu_usage": 45.2,
        "memory_usage": 67.8,
        "disk_usage": 34.1,
        "network_io": {
            "bytes_sent": 1024000,
            "bytes_received": 2048000
        },
        "active_connections": 12,
        "request_count": 1500,
        "error_rate": 0.02
    }

@router.get("/services")
async def service_status(current_user: User = Depends(get_current_user)):
    """Get status of all microservices"""
    return {
        "backend": {"status": "healthy", "uptime": "2h 30m"},
        "strategy_engine": {"status": "healthy", "uptime": "2h 25m"},
        "market_data_handler": {"status": "warning", "uptime": "1h 45m"},
        "execution_engine": {"status": "healthy", "uptime": "2h 30m"},
        "database": {"status": "healthy", "uptime": "24h"},
        "redis": {"status": "healthy", "uptime": "24h"}
    }
