from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from app.schemas.strategy import StrategyC<PERSON>, StrategyUpdate, StrategyOut
from app.services.strategy_service import (
    create_strategy, get_strategies, get_strategy, 
    update_strategy, delete_strategy, get_user_strategies
)
from app.auth.auth import get_current_user
from app.models.user import User

router = APIRouter()

@router.post("/", response_model=StrategyOut)
async def create_new_strategy(
    strategy: StrategyCreate,
    current_user: User = Depends(get_current_user)
):
    """Create a new trading strategy"""
    try:
        new_strategy = await create_strategy(strategy, current_user.id)
        return new_strategy
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to create strategy")

@router.get("/", response_model=List[StrategyOut])
async def list_strategies(
    current_user: User = Depends(get_current_user)
):
    """Get all strategies for the current user"""
    try:
        strategies = await get_user_strategies(current_user.id)
        return strategies
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch strategies")

@router.get("/{strategy_id}", response_model=StrategyOut)
async def get_strategy_detail(
    strategy_id: int,
    current_user: User = Depends(get_current_user)
):
    """Get a specific strategy by ID"""
    try:
        strategy = await get_strategy(strategy_id, current_user.id)
        if not strategy:
            raise HTTPException(status_code=404, detail="Strategy not found")
        return strategy
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch strategy")

@router.put("/{strategy_id}", response_model=StrategyOut)
async def update_strategy_detail(
    strategy_id: int,
    strategy_update: StrategyUpdate,
    current_user: User = Depends(get_current_user)
):
    """Update a strategy"""
    try:
        updated_strategy = await update_strategy(strategy_id, strategy_update, current_user.id)
        if not updated_strategy:
            raise HTTPException(status_code=404, detail="Strategy not found")
        return updated_strategy
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to update strategy")

@router.delete("/{strategy_id}")
async def delete_strategy_detail(
    strategy_id: int,
    current_user: User = Depends(get_current_user)
):
    """Delete a strategy"""
    try:
        success = await delete_strategy(strategy_id, current_user.id)
        if not success:
            raise HTTPException(status_code=404, detail="Strategy not found")
        return {"message": "Strategy deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to delete strategy")

@router.post("/{strategy_id}/start")
async def start_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_user)
):
    """Start a strategy"""
    try:
        # This would integrate with the strategy engine
        # For now, just update the status
        strategy_update = StrategyUpdate(status="active")
        updated_strategy = await update_strategy(strategy_id, strategy_update, current_user.id)
        if not updated_strategy:
            raise HTTPException(status_code=404, detail="Strategy not found")
        return {"message": "Strategy started successfully", "strategy": updated_strategy}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to start strategy")

@router.post("/{strategy_id}/stop")
async def stop_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_user)
):
    """Stop a strategy"""
    try:
        strategy_update = StrategyUpdate(status="inactive")
        updated_strategy = await update_strategy(strategy_id, strategy_update, current_user.id)
        if not updated_strategy:
            raise HTTPException(status_code=404, detail="Strategy not found")
        return {"message": "Strategy stopped successfully", "strategy": updated_strategy}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to stop strategy")
