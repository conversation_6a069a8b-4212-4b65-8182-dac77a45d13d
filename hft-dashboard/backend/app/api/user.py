from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.security import OAuth2PasswordRequestForm
from app.schemas.user import UserCreate, UserLogin, UserOut
from app.schemas.account import PasswordResetRequest, PasswordReset
from app.services.user_service import register_user, authenticate_user, create_password_reset_token, reset_password_with_token
from app.auth.auth import create_access_token, get_current_user
from sqlalchemy.exc import IntegrityError

router = APIRouter()

@router.post("/register", response_model=UserOut)
def register(user: UserCreate):
    try:
        return register_user(user)
    except IntegrityError:
        raise HTTPException(status_code=400, detail="Email already registered")

@router.post("/login", response_model=UserOut)
def login(user: UserLogin):
    user_obj = authenticate_user(user.email, user.password)
    if not user_obj:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
    return user_obj

@router.post("/token")
def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(status_code=400, detail="Incorrect email or password")
    access_token = create_access_token(data={"sub": user.email})
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserOut)
def read_users_me(current_user=Depends(get_current_user)):
    return current_user

@router.post("/password-reset-request")
def request_password_reset(request: PasswordResetRequest):
    """Request a password reset token (sends email in production)"""
    token = create_password_reset_token(request.email)
    if not token:
        raise HTTPException(status_code=404, detail="User not found")
    
    # In production, send email with reset link
    # For now, return the token (in production, this would be sent via email)
    return {"message": "Password reset token created", "token": token}

@router.post("/password-reset")
def reset_password(reset_data: PasswordReset):
    """Reset password using token"""
    success = reset_password_with_token(reset_data.token, reset_data.new_password)
    if not success:
        raise HTTPException(status_code=400, detail="Invalid or expired token")
    return {"message": "Password reset successfully"}
