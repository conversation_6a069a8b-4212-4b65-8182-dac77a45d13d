#!/bin/bash

echo "🚀 Starting HFT Dashboard Backend..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from example..."
    cp ../env.example .env
    echo "⚠️  Please update .env with your actual values"
fi

# Start required services
echo "🐘 Starting PostgreSQL and Redis..."
docker-compose up postgres redis -d

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
sleep 30

# Run database migrations
echo "🗄️  Running database migrations..."
docker-compose run --rm backend alembic upgrade head

# Start the backend
echo "🔥 Starting FastAPI backend..."
docker-compose up backend -d

echo "✅ Backend is starting up!"
echo "📊 API Documentation: http://localhost:8000/docs"
echo "🔍 Health Check: http://localhost:8000/health"
echo "📋 Testing Guide: See TESTING.md" 