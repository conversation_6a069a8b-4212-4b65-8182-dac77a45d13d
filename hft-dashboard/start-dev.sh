#!/bin/bash

echo "🚀 Starting HFT Dashboard Development Environment..."

# Check if <PERSON><PERSON> and <PERSON>er Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp env.example .env
    echo "⚠️  Please update .env file with your actual configuration values"
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data logs

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Build and start core services
echo "🔨 Building and starting core services..."
docker-compose -f docker-compose.dev.yml up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 20

# Check service health
echo "🏥 Checking service health..."

# Check backend
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Backend API is healthy"
else
    echo "❌ Backend API is not responding (may still be starting)"
fi

# Check frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is healthy"
else
    echo "❌ Frontend is not responding (may still be starting)"
fi

# Check database
if docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U hft_user > /dev/null 2>&1; then
    echo "✅ PostgreSQL is healthy"
else
    echo "❌ PostgreSQL is not responding (may still be starting)"
fi

echo ""
echo "🎉 HFT Dashboard Development Environment is starting up!"
echo ""
echo "📊 Services:"
echo "   Frontend:     http://localhost:3000"
echo "   Backend API:  http://localhost:8000"
echo "   PostgreSQL:   localhost:5433"
echo "   Redis:        localhost:6380"
echo ""
echo "📚 Documentation:"
echo "   Backend API:  http://localhost:8000/docs"
echo ""
echo "🔧 Management:"
echo "   View logs:    docker-compose -f docker-compose.dev.yml logs -f"
echo "   Stop system:  docker-compose -f docker-compose.dev.yml down"
echo "   Restart:      docker-compose -f docker-compose.dev.yml restart"
echo ""
echo "⚠️  Development Mode:"
echo "   - Hot reload enabled for frontend and backend"
echo "   - Database data persists in volumes"
echo "   - Monitoring services not included (use full setup for that)"
echo ""
echo "🚀 To start full system with monitoring:"
echo "   ./start.sh" 