#!/usr/bin/env python3
"""
Market Data Handler - HFT Dashboard
Handles real-time market data collection and processing
"""

import asyncio
import logging
import os
from typing import Dict, Any, List
import redis.asyncio as redis
from clickhouse_driver import Client
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import structlog
from datetime import datetime

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# FastAPI app
app = FastAPI(title="Market Data Handler", version="1.0.0")

# Redis connection
redis_client = None
clickhouse_client = None

class MarketData(BaseModel):
    symbol: str
    last_price: float
    change: float
    change_percent: float
    volume: int
    high: float
    low: float
    open: float
    timestamp: datetime

@app.on_event("startup")
async def startup_event():
    """Initialize connections on startup"""
    global redis_client, clickhouse_client
    
    try:
        # Connect to Redis
        redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        logger.info("Connected to Redis")
        
        # Connect to ClickHouse
        clickhouse_host = os.getenv("CLICKHOUSE_HOST", "clickhouse")
        clickhouse_port = int(os.getenv("CLICKHOUSE_PORT", "9000"))
        clickhouse_db = os.getenv("CLICKHOUSE_DB", "hft_market_data")
        
        clickhouse_client = Client(
            host=clickhouse_host,
            port=clickhouse_port,
            database=clickhouse_db
        )
        clickhouse_client.execute("SELECT 1")
        logger.info("Connected to ClickHouse")
        
        # Create market data table if not exists
        create_table_query = """
        CREATE TABLE IF NOT EXISTS market_data (
            symbol String,
            last_price Float64,
            change Float64,
            change_percent Float64,
            volume UInt64,
            high Float64,
            low Float64,
            open Float64,
            timestamp DateTime
        ) ENGINE = MergeTree()
        ORDER BY (symbol, timestamp)
        """
        clickhouse_client.execute(create_table_query)
        logger.info("Market data table ready")
        
    except Exception as e:
        logger.error("Failed to connect to services", error=str(e))
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up connections on shutdown"""
    if redis_client:
        await redis_client.close()
    if clickhouse_client:
        clickhouse_client.disconnect()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check Redis
        await redis_client.ping()
        
        # Check ClickHouse
        clickhouse_client.execute("SELECT 1")
        
        return {
            "status": "healthy",
            "services": {
                "redis": "connected",
                "clickhouse": "connected"
            }
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/market-data/{symbol}")
async def get_market_data(symbol: str):
    """Get latest market data for a symbol"""
    try:
        # Get from Redis cache first
        cached_data = await redis_client.get(f"market_data:{symbol}")
        if cached_data:
            return {"data": cached_data, "source": "cache"}
        
        # Get from ClickHouse
        query = f"""
        SELECT * FROM market_data 
        WHERE symbol = '{symbol}' 
        ORDER BY timestamp DESC 
        LIMIT 1
        """
        result = clickhouse_client.execute(query)
        
        if result:
            data = {
                "symbol": result[0][0],
                "last_price": result[0][1],
                "change": result[0][2],
                "change_percent": result[0][3],
                "volume": result[0][4],
                "high": result[0][5],
                "low": result[0][6],
                "open": result[0][7],
                "timestamp": result[0][8].isoformat()
            }
            return {"data": data, "source": "database"}
        else:
            raise HTTPException(status_code=404, detail="Symbol not found")
            
    except Exception as e:
        logger.error("Failed to get market data", symbol=symbol, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get market data")

@app.post("/market-data")
async def store_market_data(data: MarketData):
    """Store market data"""
    try:
        # Store in ClickHouse
        insert_query = """
        INSERT INTO market_data 
        (symbol, last_price, change, change_percent, volume, high, low, open, timestamp)
        VALUES
        """
        clickhouse_client.execute(
            insert_query,
            [(
                data.symbol,
                data.last_price,
                data.change,
                data.change_percent,
                data.volume,
                data.high,
                data.low,
                data.open,
                data.timestamp
            )]
        )
        
        # Cache in Redis
        await redis_client.setex(
            f"market_data:{data.symbol}",
            60,  # 1 minute cache
            data.json()
        )
        
        logger.info("Market data stored", symbol=data.symbol)
        return {"message": "Market data stored successfully"}
        
    except Exception as e:
        logger.error("Failed to store market data", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to store market data")

@app.get("/symbols")
async def list_symbols():
    """List all available symbols"""
    try:
        query = "SELECT DISTINCT symbol FROM market_data ORDER BY symbol"
        result = clickhouse_client.execute(query)
        symbols = [row[0] for row in result]
        return {"symbols": symbols}
    except Exception as e:
        logger.error("Failed to list symbols", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list symbols")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Market Data Handler",
        "version": "1.0.0",
        "status": "running"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002) 