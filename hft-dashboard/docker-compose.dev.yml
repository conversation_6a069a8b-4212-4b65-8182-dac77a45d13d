version: '3.8'

services:
  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000/ws
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - hft-network

  # Backend FastAPI Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-hft_dashboard}
      - POSTGRES_USER=${POSTGRES_USER:-hft_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-here}
      - ALGORITHM=${ALGORITHM:-HS256}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-super-secret-jwt-key-here}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - hft-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-hft_dashboard}
      - POSTGRES_USER=${POSTGRES_USER:-hft_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - hft-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - hft-network

volumes:
  postgres_data:
  redis_data:

networks:
  hft-network:
    driver: bridge 