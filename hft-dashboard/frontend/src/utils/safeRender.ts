/**
 * Safely renders a value, ensuring it's always a string or valid React element
 */
export const safeRender = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (typeof value === 'string') {
    return value;
  }
  
  if (typeof value === 'number' || typeof value === 'boolean') {
    return String(value);
  }
  
  if (typeof value === 'object') {
    // If it's an object with a message property, try to use that
    if (value.message && typeof value.message === 'string') {
      return value.message;
    }
    
    // If it's an object with a detail property, try to use that
    if (value.detail && typeof value.detail === 'string') {
      return value.detail;
    }
    
    // If it's an object with a type property (like validation errors), return a generic message
    if (value.type) {
      return 'Validation error';
    }
    
    // For any other object, return a generic message
    return 'Invalid value';
  }
  
  return String(value);
}; 