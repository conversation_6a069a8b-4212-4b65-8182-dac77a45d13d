/**
 * Safely renders a value, ensuring it's always a string or valid React element
 */
export const safeRender = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }

  if (typeof value === 'string') {
    return value;
  }

  if (typeof value === 'number' || typeof value === 'boolean') {
    return String(value);
  }

  if (typeof value === 'object') {
    // Log the object for debugging
    console.error('safeRender received object:', value);

    // If it's an object with a message property, try to use that
    if (value.message && typeof value.message === 'string') {
      return value.message;
    }

    // If it's an object with a detail property, try to use that
    if (value.detail && typeof value.detail === 'string') {
      return value.detail;
    }

    // If it's an object with a type property (like validation errors), return a generic message
    if (value.type) {
      return 'Validation error';
    }

    // Try to stringify the object to see what it contains
    try {
      const stringified = JSON.stringify(value);
      console.error('safeRender object stringified:', stringified);
      return `Error: ${stringified}`;
    } catch (e) {
      console.error('safe<PERSON><PERSON> could not stringify object:', e);
      return 'Invalid value';
    }
  }

  return String(value);
};