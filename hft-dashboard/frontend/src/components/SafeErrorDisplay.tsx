import React from 'react';
import { safeRender } from '../utils/safeRender';

interface SafeErrorDisplayProps {
  error: any;
  className?: string;
}

const SafeErrorDisplay: React.FC<SafeErrorDisplayProps> = ({ error, className = "text-danger-500" }) => {
  if (!error) {
    return null;
  }

  const errorMessage = safeRender(error);
  
  if (!errorMessage) {
    return null;
  }

  return (
    <p className={className}>
      {errorMessage}
    </p>
  );
};

export default SafeErrorDisplay; 