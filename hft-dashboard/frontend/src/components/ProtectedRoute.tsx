import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, userLoading, user } = useAuth();

  // Enhanced debug logging
  console.log('🛡️ ProtectedRoute - isAuthenticated:', isAuthenticated, 'userLoading:', userLoading, 'user:', user);
  console.log('🛡️ ProtectedRoute - Current URL:', window.location.href);

  // If not authenticated, redirect to login immediately
  if (!isAuthenticated) {
    console.log('🛡️ ProtectedRoute - NOT AUTHENTICATED - Redirecting to login');
    alert('Not authenticated - redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // If authenticated but user data is still loading, show loading screen
  // This should only happen briefly during initial load
  if (isAuthenticated && userLoading) {
    console.log('🛡️ ProtectedRoute - AUTHENTICATED but LOADING - Showing loading screen');
    alert('Authenticated but loading user data...');
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">Loading user data...</p>
        </div>
      </div>
    );
  }

  // User is authenticated, show the protected content
  console.log('🛡️ ProtectedRoute - AUTHENTICATED and LOADED - Showing protected content');
  alert('Authenticated and loaded - showing dashboard');
  return <>{children}</>;
};

export default ProtectedRoute; 