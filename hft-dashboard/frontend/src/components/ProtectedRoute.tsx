import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, userLoading } = useAuth();

  // Debug logging
  console.log('ProtectedRoute - isAuthenticated:', isAuthenticated, 'userLoading:', userLoading);

  // If not authenticated, redirect to login immediately
  if (!isAuthenticated) {
    console.log('ProtectedRoute - Redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // If authenticated but user data is still loading, show loading screen
  // This should only happen briefly during initial load
  if (isAuthenticated && userLoading) {
    console.log('ProtectedRoute - Showing loading screen');
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">Loading...</p>
        </div>
      </div>
    );
  }

  // User is authenticated, show the protected content
  console.log('ProtectedRoute - Showing protected content');
  return <>{children}</>;
};

export default ProtectedRoute; 