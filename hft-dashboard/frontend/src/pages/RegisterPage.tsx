import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { useAuth } from '../hooks/useAuth';
import { RegisterForm } from '../types';
import { safeRender } from '../utils/safeRender';
import SafeErrorDisplay from '../components/SafeErrorDisplay';

const RegisterPage: React.FC = () => {
  const { register: registerUser, registerLoading } = useAuth();
  const navigate = useNavigate();
  
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterForm>();

  const password = watch('password');

  const onSubmit = async (data: RegisterForm) => {
    try {
      await registerUser(data);
      toast.success('Registration successful! Welcome to HFT Dashboard.');
      navigate('/');
    } catch (error: any) {
      toast.error(safeRender(error.response?.data?.detail) || 'Registration failed');
    }
  };

  return (
    <div className="min-h-screen bg-dark-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
            Create your account
          </h2>
          <p className="mt-2 text-center text-sm text-dark-400">
            Or{' '}
            <Link
              to="/login"
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              sign in to existing account
            </Link>
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-white">
                Email address
              </label>
              <input
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                })}
                type="email"
                className="input-field w-full"
                placeholder="Enter your email"
              />
              <SafeErrorDisplay error={errors.email?.message} className="mt-1 text-sm text-danger-500" />
            </div>
            
            <div>
              <label htmlFor="full_name" className="block text-sm font-medium text-white">
                Full Name (Optional)
              </label>
              <input
                {...register('full_name')}
                type="text"
                className="input-field w-full"
                placeholder="Enter your full name"
              />
            </div>
            
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-white">
                Password
              </label>
              <input
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters',
                  },
                })}
                type="password"
                className="input-field w-full"
                placeholder="Enter your password"
              />
              <SafeErrorDisplay error={errors.password?.message} className="mt-1 text-sm text-danger-500" />
            </div>
            
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-white">
                Confirm Password
              </label>
              <input
                {...register('confirmPassword', {
                  required: 'Please confirm your password',
                  validate: (value) => value === password || 'Passwords do not match',
                })}
                type="password"
                className="input-field w-full"
                placeholder="Confirm your password"
              />
              <SafeErrorDisplay error={errors.confirmPassword?.message} className="mt-1 text-sm text-danger-500" />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={registerLoading}
              className="btn-primary w-full flex justify-center"
            >
              {registerLoading ? 'Creating account...' : 'Create account'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RegisterPage; 