import React, { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { useAuth } from '../hooks/useAuth';
import { LoginForm } from '../types';
import { safeRender } from '../utils/safeRender';
import SafeErrorDisplay from '../components/SafeErrorDisplay';

const LoginPage: React.FC = () => {
  const { login, loginLoading, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [isNavigating, setIsNavigating] = React.useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginForm>();

  // Redirect when authentication state changes to true
  useEffect(() => {
    if (isAuthenticated && !isNavigating) {
      console.log('🚀 LoginPage - Authentication state changed to true, redirecting to dashboard');
      setIsNavigating(true);
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate, isNavigating]);

  // Show loading screen if already authenticated or navigating
  if (isAuthenticated || isNavigating) {
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  const onSubmit = async (data: LoginForm) => {
    console.log('🚀 LoginPage - Starting login process with:', data.email);

    try {
      console.log('🚀 LoginPage - Calling login function...');
      const result = await login(data);
      console.log('✅ LoginPage - Login successful! Result:', result);
      
      toast.success('Login successful!');
      // Don't navigate here - let useEffect handle it when isAuthenticated becomes true
      
    } catch (error: any) {
      console.error('❌ LoginPage - Login error:', error);
      console.error('❌ LoginPage - Error response:', error.response);
      console.error('❌ LoginPage - Error response data:', error.response?.data);
      console.error('❌ LoginPage - Error response detail:', error.response?.data?.detail);

      const errorMessage = safeRender(error.response?.data?.detail) || safeRender(error.response?.data) || safeRender(error.message) || 'Login failed';
      console.error('❌ LoginPage - Final error message:', errorMessage);

      toast.error(errorMessage);
    }
  };

  return (
    <div className="min-h-screen bg-dark-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-dark-400">
            Or{' '}
            <Link
              to="/register"
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              create a new account
            </Link>
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-white">
                Email address
              </label>
              <input
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                })}
                type="email"
                className="input-field w-full"
                placeholder="Enter your email"
              />
              <SafeErrorDisplay error={errors.email?.message} className="mt-1 text-sm text-danger-500" />
            </div>
            
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-white">
                Password
              </label>
              <input
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters',
                  },
                })}
                type="password"
                className="input-field w-full"
                placeholder="Enter your password"
              />
              <SafeErrorDisplay error={errors.password?.message} className="mt-1 text-sm text-danger-500" />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loginLoading}
              className="btn-primary w-full flex justify-center"
            >
              {loginLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage; 