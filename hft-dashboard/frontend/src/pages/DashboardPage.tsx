import React from 'react';
import { useQuery } from 'react-query';
import { 
  ArrowTrendingUpIcon, 
  ArrowTrendingDownIcon, 
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import apiService from '../services/api';

const DashboardPage: React.FC = () => {
  const { data: account } = useQuery('account', apiService.getAccount);
  const { data: canTrade } = useQuery('canTrade', apiService.checkCanTrade);
  const { data: strategies } = useQuery('strategies', apiService.getStrategies);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Dashboard</h1>
        <p className="text-dark-400">Welcome to your HFT trading dashboard</p>
      </div>

      {/* P&L Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-primary-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-dark-400">Total Capital</p>
              <p className="text-2xl font-bold text-white">
                {account ? formatCurrency(account.capital) : '₹0'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {account?.daily_pnl && account.daily_pnl >= 0 ? (
                <ArrowTrendingUpIcon className="h-8 w-8 text-success-500" />
              ) : (
                <ArrowTrendingDownIcon className="h-8 w-8 text-danger-500" />
              )}
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-dark-400">Daily P&L</p>
              <p className={`text-2xl font-bold ${
                account?.daily_pnl && account.daily_pnl >= 0 ? 'text-success-500' : 'text-danger-500'
              }`}>
                {account ? formatCurrency(account.daily_pnl) : '₹0'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-8 w-8 text-warning-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-dark-400">Daily Loss Limit</p>
              <p className="text-2xl font-bold text-white">
                {account ? formatCurrency(account.daily_loss_limit) : '₹0'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {canTrade?.can_trade ? (
                <CheckCircleIcon className="h-8 w-8 text-success-500" />
              ) : (
                <ExclamationTriangleIcon className="h-8 w-8 text-danger-500" />
              )}
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-dark-400">Trading Status</p>
              <p className={`text-lg font-bold ${
                canTrade?.can_trade ? 'text-success-500' : 'text-danger-500'
              }`}>
                {canTrade?.can_trade ? 'Active' : 'Blocked'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-white mb-4">System Health</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-dark-300">Backend API</span>
              <span className="status-indicator status-active">Healthy</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-dark-300">Database</span>
              <span className="status-indicator status-active">Connected</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-dark-300">Market Data</span>
              <span className="status-indicator status-warning">Connecting</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-dark-300">Strategy Engine</span>
              <span className="status-indicator status-inactive">Stopped</span>
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-dark-300">Last login</span>
              <span className="text-white">2 minutes ago</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-dark-300">Active strategies</span>
              <span className="text-white">0</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-dark-300">Today's trades</span>
              <span className="text-white">0</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-dark-300">System uptime</span>
              <span className="text-white">2h 15m</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="btn-primary">
            Deploy Strategy
          </button>
          <button className="btn-secondary">
            View Market Data
          </button>
          <button className="btn-secondary">
            Account Settings
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage; 