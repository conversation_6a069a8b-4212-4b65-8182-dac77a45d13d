import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { PlusIcon, PlayIcon, StopIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import apiService from '../services/api';
import { Strategy, StrategyCreate } from '../types';

const StrategiesPage: React.FC = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingStrategy, setEditingStrategy] = useState<Strategy | null>(null);
  const queryClient = useQueryClient();

  // Fetch strategies
  const { data: strategies, isLoading, error } = useQuery<Strategy[]>(
    'strategies',
    apiService.getStrategies,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  // Create strategy mutation
  const createMutation = useMutation(
    (strategyData: StrategyCreate) => apiService.createStrategy(strategyData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('strategies');
        setShowCreateModal(false);
        toast.success('Strategy created successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to create strategy');
      }
    }
  );

  // Start/Stop strategy mutations
  const toggleMutation = useMutation(
    ({ id, action }: { id: number; action: 'start' | 'stop' }) =>
      action === 'start' 
        ? apiService.updateStrategy(id, { status: 'active' })
        : apiService.updateStrategy(id, { status: 'inactive' }),
    {
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries('strategies');
        toast.success(`Strategy ${variables.action}ed successfully`);
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to update strategy');
      }
    }
  );

  // Delete strategy mutation
  const deleteMutation = useMutation(
    (id: number) => apiService.deleteStrategy(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('strategies');
        toast.success('Strategy deleted successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to delete strategy');
      }
    }
  );

  const handleCreateStrategy = (data: StrategyCreate) => {
    createMutation.mutate(data);
  };

  const handleToggleStrategy = (strategy: Strategy) => {
    const action = strategy.status === 'active' ? 'stop' : 'start';
    toggleMutation.mutate({ id: strategy.id, action });
  };

  const handleDeleteStrategy = (id: number) => {
    if (window.confirm('Are you sure you want to delete this strategy?')) {
      deleteMutation.mutate(id);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-success-500 text-white';
      case 'inactive': return 'bg-dark-500 text-dark-300';
      case 'paused': return 'bg-warning-500 text-white';
      default: return 'bg-dark-500 text-dark-300';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Trading Strategies</h1>
          <p className="text-dark-400">Manage and monitor your trading strategies</p>
        </div>
        <button 
          onClick={() => setShowCreateModal(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>New Strategy</span>
        </button>
      </div>

      {/* Strategy Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="text-sm text-dark-400">Total Strategies</div>
          <div className="text-2xl font-bold text-white">{strategies?.length || 0}</div>
        </div>
        <div className="card">
          <div className="text-sm text-dark-400">Active</div>
          <div className="text-2xl font-bold text-success-500">
            {strategies?.filter(s => s.status === 'active').length || 0}
          </div>
        </div>
        <div className="card">
          <div className="text-sm text-dark-400">Total P&L</div>
          <div className="text-2xl font-bold text-white">
            ₹{strategies?.reduce((sum, s) => sum + s.performance.total_pnl, 0).toLocaleString() || '0'}
          </div>
        </div>
        <div className="card">
          <div className="text-sm text-dark-400">Win Rate</div>
          <div className="text-2xl font-bold text-primary-500">
            {strategies?.length ? 
              (strategies.reduce((sum, s) => sum + s.performance.win_rate, 0) / strategies.length).toFixed(1) + '%' 
              : '0%'
            }
          </div>
        </div>
      </div>

      {/* Strategies List */}
      <div className="card">
        <div className="space-y-4">
          {strategies?.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-dark-400">No strategies found. Create your first strategy to get started.</p>
            </div>
          ) : (
            strategies?.map((strategy) => (
              <div key={strategy.id} className="border border-dark-700 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-semibold text-white">{strategy.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(strategy.status)}`}>
                        {strategy.status}
                      </span>
                    </div>
                    <p className="text-dark-400 mt-1">{strategy.description}</p>
                    <div className="flex items-center space-x-4 mt-3 text-sm">
                      <span className="text-dark-300">Type: <span className="text-white">{strategy.type}</span></span>
                      <span className="text-dark-300">Symbols: <span className="text-white">{strategy.symbols.join(', ')}</span></span>
                      <span className="text-dark-300">Trades: <span className="text-white">{strategy.performance.total_trades}</span></span>
                    </div>
                    <div className="grid grid-cols-3 gap-4 mt-4">
                      <div>
                        <span className="text-dark-400 text-xs">Total P&L</span>
                        <div className={`font-bold ${strategy.performance.total_pnl >= 0 ? 'text-success-500' : 'text-danger-500'}`}>
                          ₹{strategy.performance.total_pnl.toLocaleString()}
                        </div>
                      </div>
                      <div>
                        <span className="text-dark-400 text-xs">Win Rate</span>
                        <div className="font-bold text-white">{strategy.performance.win_rate.toFixed(1)}%</div>
                      </div>
                      <div>
                        <span className="text-dark-400 text-xs">Sharpe Ratio</span>
                        <div className="font-bold text-white">{strategy.performance.sharpe_ratio.toFixed(2)}</div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleToggleStrategy(strategy)}
                      className={`p-2 rounded-lg ${
                        strategy.status === 'active' 
                          ? 'bg-danger-500 hover:bg-danger-600' 
                          : 'bg-success-500 hover:bg-success-600'
                      } text-white`}
                      disabled={toggleMutation.isLoading}
                    >
                      {strategy.status === 'active' ? 
                        <StopIcon className="h-4 w-4" /> : 
                        <PlayIcon className="h-4 w-4" />
                      }
                    </button>
                    <button
                      onClick={() => setEditingStrategy(strategy)}
                      className="p-2 rounded-lg bg-primary-500 hover:bg-primary-600 text-white"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteStrategy(strategy.id)}
                      className="p-2 rounded-lg bg-danger-500 hover:bg-danger-600 text-white"
                      disabled={deleteMutation.isLoading}
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Create Strategy Modal */}
      {showCreateModal && (
        <CreateStrategyModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateStrategy}
          isLoading={createMutation.isLoading}
        />
      )}
    </div>
  );
};

// Create Strategy Modal Component
const CreateStrategyModal: React.FC<{
  onClose: () => void;
  onSubmit: (data: StrategyCreate) => void;
  isLoading: boolean;
}> = ({ onClose, onSubmit, isLoading }) => {
  const [formData, setFormData] = useState<StrategyCreate>({
    name: '',
    description: '',
    type: 'momentum',
    symbols: ['NIFTY'],
    parameters: {}
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-dark-800 rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold text-white mb-4">Create New Strategy</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-1">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="input-field w-full"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-white mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="input-field w-full"
              rows={3}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-white mb-1">Type</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              className="input-field w-full"
            >
              <option value="momentum">Momentum</option>
              <option value="mean_reversion">Mean Reversion</option>
              <option value="arbitrage">Arbitrage</option>
              <option value="custom">Custom</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-white mb-1">Symbols</label>
            <input
              type="text"
              value={formData.symbols.join(', ')}
              onChange={(e) => setFormData({ 
                ...formData, 
                symbols: e.target.value.split(',').map(s => s.trim()).filter(s => s)
              })}
              className="input-field w-full"
              placeholder="NIFTY, BANKNIFTY"
              required
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Create Strategy'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StrategiesPage; 