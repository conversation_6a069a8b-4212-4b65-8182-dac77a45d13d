import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { ArrowUpIcon, ArrowDownIcon, ChartBarIcon, ClockIcon } from '@heroicons/react/24/outline';
import apiService from '../services/api';
import { MarketData, OrderBook } from '../types';

const MarketDataPage: React.FC = () => {
  const [selectedSymbol, setSelectedSymbol] = useState<string>('NIFTY');
  const [activeTab, setActiveTab] = useState<'overview' | 'orderbook' | 'charts'>('overview');

  // Fetch market overview
  const { data: marketOverview, isLoading: overviewLoading } = useQuery(
    'marketOverview',
    () => apiService.getMarketData(''),
    {
      refetchInterval: 5000, // Refresh every 5 seconds
    }
  );

  // Fetch specific symbol data
  const { data: symbolData, isLoading: symbolLoading } = useQuery(
    ['marketData', selectedSymbol],
    () => apiService.getMarketData(selectedSymbol),
    {
      enabled: !!selectedSymbol,
      refetchInterval: 2000, // Refresh every 2 seconds
    }
  );

  // Fetch order book
  const { data: orderBook, isLoading: orderBookLoading } = useQuery(
    ['orderBook', selectedSymbol],
    () => apiService.getOrderBook(selectedSymbol),
    {
      enabled: !!selectedSymbol && activeTab === 'orderbook',
      refetchInterval: 1000, // Refresh every 1 second for order book
    }
  );

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
    }).format(price);
  };

  const formatVolume = (volume: number) => {
    if (volume >= ********) {
      return (volume / ********).toFixed(1) + ' Cr';
    } else if (volume >= 100000) {
      return (volume / 100000).toFixed(1) + ' L';
    } else if (volume >= 1000) {
      return (volume / 1000).toFixed(1) + ' K';
    }
    return volume.toString();
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? 'text-success-500' : 'text-danger-500';
  };

  const majorSymbols = ['NIFTY', 'BANKNIFTY', 'NIFTY50', 'FINNIFTY'];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-white">Market Data</h1>
        <p className="text-dark-400">Real-time market data and analytics</p>
      </div>

      {/* Market Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {majorSymbols.map((symbol) => {
          // Mock data for now since we don't have real market data
          const mockData = {
            symbol,
            last_price: symbol === 'NIFTY' ? 18500 : symbol === 'BANKNIFTY' ? 44500 : 18300,
            change: Math.random() * 100 - 50,
            change_percent: (Math.random() * 2 - 1).toFixed(2),
            volume: Math.floor(Math.random() * ********),
            high: 0,
            low: 0,
            open: 0,
            timestamp: new Date().toISOString()
          };

          return (
            <div 
              key={symbol}
              className={`card cursor-pointer transition-colors ${
                selectedSymbol === symbol ? 'border-primary-500' : 'hover:border-dark-600'
              }`}
              onClick={() => setSelectedSymbol(symbol)}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold text-white">{symbol}</h3>
                  <div className="text-2xl font-bold text-white">
                    {formatPrice(mockData.last_price)}
                  </div>
                </div>
                <div className={`flex items-center space-x-1 ${getChangeColor(mockData.change)}`}>
                  {mockData.change >= 0 ? (
                    <ArrowUpIcon className="h-4 w-4" />
                  ) : (
                    <ArrowDownIcon className="h-4 w-4" />
                  )}
                  <span className="text-sm font-medium">
                    {mockData.change_percent}%
                  </span>
                </div>
              </div>
              <div className="mt-3 text-sm text-dark-400">
                <div>Change: <span className={getChangeColor(mockData.change)}>
                  {formatPrice(Math.abs(mockData.change))}
                </span></div>
                <div>Volume: {formatVolume(mockData.volume)}</div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Symbol Details */}
      <div className="card">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">{selectedSymbol} Details</h2>
          <div className="flex space-x-1">
            {(['overview', 'orderbook', 'charts'] as const).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-4 py-2 rounded-lg text-sm font-medium ${
                  activeTab === tab
                    ? 'bg-primary-500 text-white'
                    : 'bg-dark-700 text-dark-300 hover:bg-dark-600'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {activeTab === 'overview' && (
          <div className="space-y-6">
            {symbolLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
            ) : (
              <>
                {/* Price Information */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-dark-700 rounded-lg p-4">
                    <div className="text-sm text-dark-400 mb-1">Last Price</div>
                    <div className="text-2xl font-bold text-white">
                      {formatPrice(18500)} {/* Mock data */}
                    </div>
                  </div>
                  <div className="bg-dark-700 rounded-lg p-4">
                    <div className="text-sm text-dark-400 mb-1">Day High</div>
                    <div className="text-xl font-bold text-success-500">
                      {formatPrice(18650)} {/* Mock data */}
                    </div>
                  </div>
                  <div className="bg-dark-700 rounded-lg p-4">
                    <div className="text-sm text-dark-400 mb-1">Day Low</div>
                    <div className="text-xl font-bold text-danger-500">
                      {formatPrice(18420)} {/* Mock data */}
                    </div>
                  </div>
                </div>

                {/* OHLC Data */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-sm text-dark-400">Open</div>
                    <div className="text-lg font-bold text-white">{formatPrice(18475)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-dark-400">High</div>
                    <div className="text-lg font-bold text-success-500">{formatPrice(18650)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-dark-400">Low</div>
                    <div className="text-lg font-bold text-danger-500">{formatPrice(18420)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-dark-400">Close</div>
                    <div className="text-lg font-bold text-white">{formatPrice(18500)}</div>
                  </div>
                </div>

                {/* Volume and other metrics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-dark-700 rounded-lg p-4">
                    <div className="text-sm text-dark-400 mb-1">Volume</div>
                    <div className="text-xl font-bold text-white">
                      {formatVolume(1500000)} {/* Mock data */}
                    </div>
                  </div>
                  <div className="bg-dark-700 rounded-lg p-4">
                    <div className="text-sm text-dark-400 mb-1">Market Cap</div>
                    <div className="text-xl font-bold text-white">₹185.5 Cr</div>
                  </div>
                  <div className="bg-dark-700 rounded-lg p-4">
                    <div className="text-sm text-dark-400 mb-1">Last Updated</div>
                    <div className="text-sm text-white flex items-center">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      {new Date().toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {activeTab === 'orderbook' && (
          <div className="space-y-4">
            {orderBookLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Bids */}
                <div>
                  <h3 className="text-lg font-semibold text-success-500 mb-4">Bids</h3>
                  <div className="space-y-2">
                    <div className="grid grid-cols-3 gap-4 text-sm text-dark-400 pb-2 border-b border-dark-700">
                      <div>Price</div>
                      <div>Quantity</div>
                      <div>Total</div>
                    </div>
                    {/* Mock bid data */}
                    {[
                      { price: 18499, quantity: 100, total: 1849900 },
                      { price: 18498, quantity: 200, total: 3699600 },
                      { price: 18497, quantity: 150, total: 2774550 },
                      { price: 18496, quantity: 300, total: 5548800 },
                      { price: 18495, quantity: 250, total: 4623750 }
                    ].map((bid, index) => (
                      <div key={index} className="grid grid-cols-3 gap-4 text-sm text-success-500">
                        <div>{formatPrice(bid.price)}</div>
                        <div>{bid.quantity}</div>
                        <div>{formatPrice(bid.total)}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Asks */}
                <div>
                  <h3 className="text-lg font-semibold text-danger-500 mb-4">Asks</h3>
                  <div className="space-y-2">
                    <div className="grid grid-cols-3 gap-4 text-sm text-dark-400 pb-2 border-b border-dark-700">
                      <div>Price</div>
                      <div>Quantity</div>
                      <div>Total</div>
                    </div>
                    {/* Mock ask data */}
                    {[
                      { price: 18501, quantity: 75, total: 1387575 },
                      { price: 18502, quantity: 125, total: 2312750 },
                      { price: 18503, quantity: 100, total: 1850300 },
                      { price: 18504, quantity: 200, total: 3700800 },
                      { price: 18505, quantity: 175, total: 3238375 }
                    ].map((ask, index) => (
                      <div key={index} className="grid grid-cols-3 gap-4 text-sm text-danger-500">
                        <div>{formatPrice(ask.price)}</div>
                        <div>{ask.quantity}</div>
                        <div>{formatPrice(ask.total)}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'charts' && (
          <div className="text-center py-12">
            <ChartBarIcon className="h-16 w-16 text-dark-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Charts Coming Soon</h3>
            <p className="text-dark-400">
              Interactive price charts and technical analysis tools will be available soon.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MarketDataPage; 