// User Types
export interface User {
  id: number;
  email: string;
  full_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface UserCreate {
  email: string;
  password: string;
  full_name?: string;
}

export interface UserLogin {
  email: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
}

// Account Types
export interface Account {
  id: number;
  user_id: number;
  capital: number;
  daily_loss_limit: number;
  daily_pnl: number;
  is_trading_blocked: boolean;
  created_at: string;
  updated_at?: string;
}

export interface AccountUpdate {
  capital?: number;
  daily_loss_limit?: number;
  daily_pnl?: number;
  is_trading_blocked?: boolean;
}

// Strategy Types
export interface Strategy {
  id: number;
  name: string;
  description?: string;
  status: 'active' | 'inactive' | 'paused';
  type: 'momentum' | 'mean_reversion' | 'arbitrage' | 'custom';
  symbols: string[];
  parameters: Record<string, any>;
  performance: StrategyPerformance;
  created_at: string;
  updated_at?: string;
}

export interface StrategyPerformance {
  total_pnl: number;
  daily_pnl: number;
  win_rate: number;
  total_trades: number;
  sharpe_ratio: number;
  max_drawdown: number;
}

export interface StrategyCreate {
  name: string;
  description?: string;
  type: string;
  symbols: string[];
  parameters: Record<string, any>;
}

// Market Data Types
export interface MarketData {
  symbol: string;
  last_price: number;
  change: number;
  change_percent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  timestamp: string;
}

export interface OrderBook {
  symbol: string;
  bids: OrderBookEntry[];
  asks: OrderBookEntry[];
  timestamp: string;
}

export interface OrderBookEntry {
  price: number;
  quantity: number;
  total: number;
}

// Trade Types
export interface Trade {
  id: number;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  timestamp: string;
  strategy_id?: number;
  user_id: number;
}

export interface Order {
  id: number;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop';
  quantity: number;
  price?: number;
  status: 'pending' | 'filled' | 'cancelled' | 'rejected';
  filled_quantity: number;
  average_price?: number;
  timestamp: string;
  user_id: number;
}

// System Health Types
export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  components: {
    database: HealthStatus;
    redis: HealthStatus;
    market_data: HealthStatus;
    execution_engine: HealthStatus;
    strategy_engine: HealthStatus;
  };
  metrics: {
    latency_ms: number;
    memory_usage_percent: number;
    cpu_usage_percent: number;
    active_connections: number;
  };
  last_updated: string;
}

export interface HealthStatus {
  status: 'healthy' | 'warning' | 'critical';
  message?: string;
  last_check: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Chart Data Types
export interface ChartDataPoint {
  timestamp: string;
  value: number;
}

export interface PnLChartData {
  symbol: string;
  data: ChartDataPoint[];
}

// Notification Types
export interface Notification {
  id: number;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  read: boolean;
  timestamp: string;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  full_name?: string;
}

export interface PasswordResetForm {
  email: string;
}

export interface NewPasswordForm {
  token: string;
  new_password: string;
  confirm_password: string;
} 