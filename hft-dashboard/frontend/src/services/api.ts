import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  User, UserCreate, UserLogin, AuthResponse, 
  Account, AccountUpdate, Strategy, StrategyCreate,
  MarketData, OrderBook, Trade, Order, SystemHealth,
  ApiResponse, PaginatedResponse 
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Load token from localStorage
    this.token = localStorage.getItem('access_token');
    if (this.token) {
      this.setAuthToken(this.token);
    }

    // Add response interceptor for token refresh
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.logout();
        }
        return Promise.reject(error);
      }
    );
  }

  private setAuthToken(token: string) {
    this.token = token;
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('access_token', token);
  }

  private clearAuthToken() {
    this.token = null;
    delete this.api.defaults.headers.common['Authorization'];
    localStorage.removeItem('access_token');
  }

  // Authentication
  async login(credentials: UserLogin): Promise<AuthResponse> {
    const formData = new FormData();
    formData.append('username', credentials.email);
    formData.append('password', credentials.password);

    const response: AxiosResponse<AuthResponse> = await this.api.post('/users/token', formData);
    this.setAuthToken(response.data.access_token);
    return response.data;
  }

  async register(userData: UserCreate): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/users/register', userData);
    this.setAuthToken(response.data.access_token);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get('/users/me');
    return response.data;
  }

  async requestPasswordReset(email: string): Promise<{ message: string; token?: string }> {
    const response: AxiosResponse<{ message: string; token?: string }> = await this.api.post('/users/password-reset-request', { email });
    return response.data;
  }

  async resetPassword(token: string, newPassword: string): Promise<{ message: string }> {
    const response: AxiosResponse<{ message: string }> = await this.api.post('/users/password-reset', { token, new_password: newPassword });
    return response.data;
  }

  logout() {
    this.clearAuthToken();
    window.location.href = '/login';
  }

  // Account Management
  async getAccount(): Promise<Account> {
    const response: AxiosResponse<Account> = await this.api.get('/accounts/me');
    return response.data;
  }

  async updateAccount(accountData: AccountUpdate): Promise<Account> {
    const response: AxiosResponse<Account> = await this.api.put('/accounts/me', accountData);
    return response.data;
  }

  async checkCanTrade(): Promise<{ can_trade: boolean }> {
    const response: AxiosResponse<{ can_trade: boolean }> = await this.api.get('/accounts/me/can-trade');
    return response.data;
  }

  // Health Check
  async getHealth(): Promise<{ status: string }> {
    const response: AxiosResponse<{ status: string }> = await this.api.get('/health');
    return response.data;
  }

  // Market Data (placeholder endpoints - to be implemented in backend)
  async getMarketData(symbol: string): Promise<MarketData> {
    const response: AxiosResponse<MarketData> = await this.api.get(`/market-data/${symbol}`);
    return response.data;
  }

  async getOrderBook(symbol: string): Promise<OrderBook> {
    const response: AxiosResponse<OrderBook> = await this.api.get(`/market-data/${symbol}/orderbook`);
    return response.data;
  }

  // Strategies (placeholder endpoints - to be implemented in backend)
  async getStrategies(): Promise<Strategy[]> {
    const response: AxiosResponse<Strategy[]> = await this.api.get('/strategies');
    return response.data;
  }

  async createStrategy(strategyData: StrategyCreate): Promise<Strategy> {
    const response: AxiosResponse<Strategy> = await this.api.post('/strategies', strategyData);
    return response.data;
  }

  async updateStrategy(id: number, strategyData: Partial<Strategy>): Promise<Strategy> {
    const response: AxiosResponse<Strategy> = await this.api.put(`/strategies/${id}`, strategyData);
    return response.data;
  }

  async deleteStrategy(id: number): Promise<void> {
    await this.api.delete(`/strategies/${id}`);
  }

  // Trades and Orders (placeholder endpoints - to be implemented in backend)
  async getTrades(page: number = 1, limit: number = 50): Promise<PaginatedResponse<Trade>> {
    const response: AxiosResponse<PaginatedResponse<Trade>> = await this.api.get(`/trades?page=${page}&limit=${limit}`);
    return response.data;
  }

  async getOrders(page: number = 1, limit: number = 50): Promise<PaginatedResponse<Order>> {
    const response: AxiosResponse<PaginatedResponse<Order>> = await this.api.get(`/orders?page=${page}&limit=${limit}`);
    return response.data;
  }

  // System Health (placeholder endpoint - to be implemented in backend)
  async getSystemHealth(): Promise<SystemHealth> {
    const response: AxiosResponse<SystemHealth> = await this.api.get('/system/health');
    return response.data;
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.token;
  }

  getToken(): string | null {
    return this.token;
  }
}

export const apiService = new ApiService();
export default apiService; 