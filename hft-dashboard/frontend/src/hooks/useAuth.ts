import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { User, UserLogin, UserCreate } from '../types';
import apiService from '../services/api';

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const queryClient = useQueryClient();

  // Check authentication status on mount
  useEffect(() => {
    const authStatus = apiService.isAuthenticated();
    console.log('useAuth - Initial auth status:', authStatus);
    setIsAuthenticated(authStatus);
  }, []);

  // Get current user
  const { data: user, isLoading: userLoading, error: userError } = useQuery<User>(
    'currentUser',
    apiService.getCurrentUser,
    {
      enabled: isAuthenticated,
      retry: false,
      onError: () => {
        console.log('useAuth - getCurrentUser failed, logging out');
        setIsAuthenticated(false);
        apiService.logout();
      }
    }
  );

  // Login mutation
  const loginMutation = useMutation(
    (credentials: UserLogin) => apiService.login(credentials),
    {
      onSuccess: (data) => {
        console.log('useAuth - Login mutation success, setting authenticated to true');
        setIsAuthenticated(true);
        // If the login response includes user data, set it immediately
        if (data.user) {
          console.log('useAuth - Setting user data from login response');
          queryClient.setQueryData('currentUser', data.user);
        }
        // Then fetch fresh user data in the background
        queryClient.invalidateQueries('currentUser');
      },
      onError: (error) => {
        console.error('useAuth - Login mutation error:', error);
        setIsAuthenticated(false);
      }
    }
  );

  // Register mutation
  const registerMutation = useMutation(
    (userData: UserCreate) => apiService.register(userData),
    {
      onSuccess: (data) => {
        console.log('useAuth - Register mutation success, setting authenticated to true');
        // Auto-login after registration since we now get a token
        setIsAuthenticated(true);
        // Set the user data immediately from the registration response
        queryClient.setQueryData('currentUser', data.user);
        // Then fetch fresh user data in the background
        queryClient.invalidateQueries('currentUser');
      }
    }
  );

  // Logout
  const logout = () => {
    console.log('useAuth - Logging out');
    apiService.logout();
    setIsAuthenticated(false);
    queryClient.clear();
  };

  // Debug logging for authentication state changes
  useEffect(() => {
    console.log('useAuth - Authentication state changed:', isAuthenticated);
  }, [isAuthenticated]);

  return {
    user,
    isAuthenticated,
    userLoading,
    userError,
    login: loginMutation.mutateAsync,
    loginLoading: loginMutation.isLoading,
    loginError: loginMutation.error,
    register: registerMutation.mutateAsync,
    registerLoading: registerMutation.isLoading,
    registerError: registerMutation.error,
    logout
  };
}; 