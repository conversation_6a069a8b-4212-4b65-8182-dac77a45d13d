import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { User, UserLogin, UserCreate } from '../types';
import apiService from '../services/api';

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const queryClient = useQueryClient();

  // Check authentication status on mount
  useEffect(() => {
    setIsAuthenticated(apiService.isAuthenticated());
  }, []);

  // Get current user
  const { data: user, isLoading: userLoading, error: userError } = useQuery<User>(
    'currentUser',
    apiService.getCurrentUser,
    {
      enabled: isAuthenticated,
      retry: false,
      onError: () => {
        setIsAuthenticated(false);
        apiService.logout();
      }
    }
  );

  // Login mutation
  const loginMutation = useMutation(
    (credentials: UserLogin) => apiService.login(credentials),
    {
      onSuccess: () => {
        setIsAuthenticated(true);
        queryClient.invalidateQueries('currentUser');
      },
      onError: (error) => {
        console.error('Login mutation error:', error);
        setIsAuthenticated(false);
      }
    }
  );

  // Register mutation
  const registerMutation = useMutation(
    (userData: UserCreate) => apiService.register(userData),
    {
      onSuccess: () => {
        // Auto-login after registration since we now get a token
        setIsAuthenticated(true);
        queryClient.invalidateQueries('currentUser');
      }
    }
  );

  // Logout
  const logout = () => {
    apiService.logout();
    setIsAuthenticated(false);
    queryClient.clear();
  };

  return {
    user,
    isAuthenticated,
    userLoading,
    userError,
    login: loginMutation.mutate,
    loginLoading: loginMutation.isLoading,
    loginError: loginMutation.error,
    register: registerMutation.mutate,
    registerLoading: registerMutation.isLoading,
    registerError: registerMutation.error,
    logout
  };
}; 