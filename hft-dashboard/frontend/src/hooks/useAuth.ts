import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { User, UserLogin, UserCreate } from '../types';
import apiService from '../services/api';

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    // Initialize with current auth status to avoid initial flicker
    return apiService.isAuthenticated();
  });
  const queryClient = useQueryClient();

  // Check authentication status on mount - but only if not already authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      const authStatus = apiService.isAuthenticated();
      console.log('useAuth - Checking auth status on mount:', authStatus);
      if (authStatus !== isAuthenticated) {
        setIsAuthenticated(authStatus);
      }
    }
  }, [isAuthenticated]);

  // Get current user
  const { data: user, isLoading: userLoading, error: userError } = useQuery<User>(
    'currentUser',
    apiService.getCurrentUser,
    {
      enabled: isAuthenticated,
      retry: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      onError: (error: any) => {
        console.log('❌ useAuth - getCurrentUser failed:', error);
        // Only logout if it's actually an auth error, not a network error
        if (error?.response?.status === 401) {
          console.log('❌ useAuth - 401 error, logging out');
          setIsAuthenticated(false);
          apiService.logout();
        } else {
          console.log('❌ useAuth - Non-auth error, not logging out');
        }
      }
    }
  );

  // Login mutation
  const loginMutation = useMutation(
    (credentials: UserLogin) => apiService.login(credentials),
    {
      onSuccess: (data) => {
        console.log('🔐 useAuth - Login mutation success! Data:', data);
        
        // Verify token was received and is stored
        if (data.access_token) {
          console.log('🔐 useAuth - Received valid token, first 10 chars:', data.access_token.substring(0, 10) + '...');
          // Force token to localStorage as a backup in case apiService.login didn't do it
          localStorage.setItem('access_token', data.access_token);
        } else {
          console.error('🔐 useAuth - No access_token in response!');
        }
        
        // Double-check token in localStorage
        const storedToken = localStorage.getItem('access_token');
        console.log('🔐 useAuth - Token in localStorage exists:', !!storedToken);
        
        console.log('🔐 useAuth - Setting authenticated to true');
        setIsAuthenticated(true);

        // If the login response includes user data, set it immediately
        if (data.user) {
          console.log('🔐 useAuth - Setting user data from login response:', data.user);
          queryClient.setQueryData('currentUser', data.user);
        }

        // Invalidate the currentUser query to make sure we get fresh data
        console.log('🔐 useAuth - Invalidating currentUser query to fetch latest data');
        setTimeout(() => queryClient.invalidateQueries('currentUser'), 500);

        console.log('🔐 useAuth - Login mutation complete - isAuthenticated should now be true');
      },
      onError: (error: any) => {
        console.error('❌ useAuth - Login mutation error:', error);
        setIsAuthenticated(false);
      }
    }
  );

  // Register mutation
  const registerMutation = useMutation(
    (userData: UserCreate) => apiService.register(userData),
    {
      onSuccess: (data: any) => {
        console.log('useAuth - Register mutation success, setting authenticated to true');
        // Auto-login after registration since we now get a token
        setIsAuthenticated(true);
        // Set the user data immediately from the registration response
        queryClient.setQueryData('currentUser', data.user);
        // Then fetch fresh user data in the background
        queryClient.invalidateQueries('currentUser');
      }
    }
  );

  // Logout
  const logout = () => {
    console.log('useAuth - Logging out');
    apiService.logout();
    setIsAuthenticated(false);
    queryClient.clear();
  };

  // Debug logging for authentication state changes
  useEffect(() => {
    console.log('useAuth - Authentication state changed:', isAuthenticated);
  }, [isAuthenticated]);

  return {
    user,
    isAuthenticated,
    userLoading,
    userError,
    login: loginMutation.mutateAsync,
    loginLoading: loginMutation.isLoading,
    loginError: loginMutation.error,
    register: registerMutation.mutateAsync,
    registerLoading: registerMutation.isLoading,
    registerError: registerMutation.error,
    logout
  };
}; 