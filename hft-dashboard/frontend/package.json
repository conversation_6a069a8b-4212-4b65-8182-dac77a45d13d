{"name": "hft-dashboard-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.56", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.7.4", "web-vitals": "^2.1.4", "react-router-dom": "^6.3.0", "axios": "^1.4.0", "react-query": "^3.39.3", "recharts": "^2.6.2", "react-hook-form": "^7.43.9", "react-hot-toast": "^2.4.0", "lucide-react": "^0.263.1", "clsx": "^1.2.1", "tailwind-merge": "^1.13.2", "date-fns": "^2.30.0", "react-dropzone": "^14.2.3", "react-table": "^7.8.0", "@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.3.2", "autoprefixer": "^10.4.14", "postcss": "^8.4.24"}}