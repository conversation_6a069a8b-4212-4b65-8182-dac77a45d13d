# HFT Dashboard Frontend

A modern React-based frontend for the HFT Dashboard system, built with TypeScript, Tailwind CSS, and React Query.

## 🚀 Features

- **Modern UI/UX** - Clean, responsive design with dark theme
- **Real-time Dashboard** - Live P&L, positions, and system health
- **User Authentication** - Login, registration, and password reset
- **Account Management** - Capital tracking and daily loss limits
- **Strategy Management** - Deploy and monitor trading strategies
- **Market Data Visualization** - Charts and real-time data
- **System Monitoring** - Health checks and performance metrics

## 🛠️ Tech Stack

- **React 18** - Modern React with hooks
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **React Query** - Server state management
- **React Router** - Client-side routing
- **React Hook Form** - Form handling and validation
- **Axios** - HTTP client
- **Heroicons** - Beautiful icons
- **Recharts** - Chart components

## 📋 Prerequisites

- **Node.js** 18.x or later
- **npm** or **yarn**
- **Backend API** running (see backend README)

## 🚀 Quick Start

### 1. Install Dependencies

```bash
cd frontend
npm install
```

### 2. Environment Configuration

Create a `.env` file in the frontend directory:

```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws
REACT_APP_GRAFANA_URL=http://localhost:3001
```

### 3. Start Development Server

```bash
# Using npm
npm start

# Using the startup script
./start.sh
```

The app will be available at: http://localhost:3000

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Layout.tsx      # Main layout with sidebar
│   └── ProtectedRoute.tsx
├── pages/              # Page components
│   ├── LoginPage.tsx
│   ├── RegisterPage.tsx
│   ├── DashboardPage.tsx
│   ├── StrategiesPage.tsx
│   ├── MarketDataPage.tsx
│   └── AccountPage.tsx
├── hooks/              # Custom React hooks
│   └── useAuth.ts
├── services/           # API services
│   └── api.ts
├── types/              # TypeScript type definitions
│   └── index.ts
├── utils/              # Utility functions
├── App.tsx             # Main app component
├── index.tsx           # Entry point
└── index.css           # Global styles
```

## 🎨 UI Components

### Styled Components

The app uses Tailwind CSS with custom components:

- `.btn-primary` - Primary action buttons
- `.btn-secondary` - Secondary action buttons
- `.btn-success` - Success action buttons
- `.btn-danger` - Danger action buttons
- `.card` - Card containers
- `.input-field` - Form input fields
- `.status-indicator` - Status badges

### Color Scheme

- **Primary**: Blue (#3b82f6)
- **Success**: Green (#22c55e)
- **Danger**: Red (#ef4444)
- **Warning**: Yellow (#f59e0b)
- **Dark**: Gray scale (#0f172a to #f8fafc)

## 🔐 Authentication

The app uses JWT-based authentication:

1. **Login** - Email/password authentication
2. **Registration** - New user account creation
3. **Password Reset** - Email-based password reset
4. **Protected Routes** - Automatic redirect to login
5. **Token Management** - Automatic token storage and refresh

## 📊 Dashboard Features

### Real-time P&L
- Total capital display
- Daily P&L tracking
- Loss limit monitoring
- Trading status indicators

### System Health
- Backend API status
- Database connectivity
- Market data feed status
- Strategy engine status

### Quick Actions
- Deploy new strategies
- View market data
- Access account settings

## 🔧 Development

### Available Scripts

```bash
npm start          # Start development server
npm run build      # Build for production
npm test           # Run tests
npm run eject      # Eject from Create React App
```

### Code Style

The project uses:
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Type checking

### State Management

- **React Query** - Server state (API calls, caching)
- **React Context** - Global app state
- **Local State** - Component-specific state

## 🐳 Docker Deployment

### Build Production Image

```bash
docker build -t hft-dashboard-frontend .
```

### Run with Docker Compose

```bash
# From the root directory
docker-compose up frontend
```

## 🔗 API Integration

The frontend communicates with the backend via:

- **REST API** - HTTP requests for CRUD operations
- **WebSocket** - Real-time data streaming (planned)
- **JWT Tokens** - Authentication and authorization

### API Endpoints

- `/users/*` - User management
- `/accounts/*` - Account management
- `/strategies/*` - Strategy management
- `/market-data/*` - Market data
- `/trades/*` - Trade history
- `/orders/*` - Order management

## 🧪 Testing

### Run Tests

```bash
npm test
```

### Test Coverage

```bash
npm test -- --coverage
```

## 📱 Responsive Design

The app is fully responsive and works on:
- **Desktop** - Full dashboard experience
- **Tablet** - Optimized layout
- **Mobile** - Mobile-friendly interface

## 🚀 Performance

### Optimizations

- **Code Splitting** - Lazy loading of routes
- **Image Optimization** - Compressed images
- **Bundle Analysis** - Optimized bundle size
- **Caching** - Browser and API caching

### Monitoring

- **Error Tracking** - Error boundary implementation
- **Performance Metrics** - Core Web Vitals
- **Analytics** - User behavior tracking (planned)

## 🔒 Security

### Security Features

- **HTTPS** - Secure communication
- **CORS** - Cross-origin protection
- **XSS Protection** - Input sanitization
- **CSRF Protection** - Token-based protection
- **Content Security Policy** - Resource restrictions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the troubleshooting guide
- Create an issue in the repository

---

**Note**: This frontend is designed to work with the HFT Dashboard backend. Please ensure the backend is running before starting the frontend. 