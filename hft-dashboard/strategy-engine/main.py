#!/usr/bin/env python3
"""
Strategy Engine - HFT Dashboard
Handles strategy execution and management
"""

import asyncio
import logging
import os
from typing import Dict, Any
import redis.asyncio as redis
from clickhouse_driver import Client
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import structlog

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# FastAPI app
app = FastAPI(title="Strategy Engine", version="1.0.0")

# Redis connection
redis_client = None
clickhouse_client = None

class StrategyConfig(BaseModel):
    name: str
    type: str
    parameters: Dict[str, Any]
    symbols: list[str]

@app.on_event("startup")
async def startup_event():
    """Initialize connections on startup"""
    global redis_client, clickhouse_client
    
    try:
        # Connect to Redis
        redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        logger.info("Connected to Redis")
        
        # Connect to ClickHouse
        clickhouse_host = os.getenv("CLICKHOUSE_HOST", "clickhouse")
        clickhouse_port = int(os.getenv("CLICKHOUSE_PORT", "9000"))
        clickhouse_db = os.getenv("CLICKHOUSE_DB", "hft_market_data")
        
        clickhouse_client = Client(
            host=clickhouse_host,
            port=clickhouse_port,
            database=clickhouse_db
        )
        clickhouse_client.execute("SELECT 1")
        logger.info("Connected to ClickHouse")
        
    except Exception as e:
        logger.error("Failed to connect to services", error=str(e))
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up connections on shutdown"""
    if redis_client:
        await redis_client.close()
    if clickhouse_client:
        clickhouse_client.disconnect()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check Redis
        await redis_client.ping()
        
        # Check ClickHouse
        clickhouse_client.execute("SELECT 1")
        
        return {
            "status": "healthy",
            "services": {
                "redis": "connected",
                "clickhouse": "connected"
            }
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/strategies")
async def list_strategies():
    """List all available strategies"""
    try:
        strategies = await redis_client.hgetall("strategies")
        return {"strategies": strategies}
    except Exception as e:
        logger.error("Failed to list strategies", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list strategies")

@app.post("/strategies")
async def create_strategy(config: StrategyConfig):
    """Create a new strategy"""
    try:
        strategy_id = f"strategy:{config.name}"
        await redis_client.hset("strategies", strategy_id, config.json())
        logger.info("Strategy created", strategy_name=config.name)
        return {"message": "Strategy created successfully", "strategy_id": strategy_id}
    except Exception as e:
        logger.error("Failed to create strategy", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create strategy")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Strategy Engine",
        "version": "1.0.0",
        "status": "running"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001) 