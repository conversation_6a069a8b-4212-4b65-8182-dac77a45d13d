FROM gcc:11

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    cmake \
    libpq-dev \
    libhiredis-dev \
    libssl-dev \
    libboost-system-dev \
    libboost-thread-dev \
    libboost-chrono-dev \
    libboost-filesystem-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Copy source code
COPY . .

# Build the application
RUN mkdir build && cd build && \
    cmake .. && \
    make -j$(nproc)

# Create data directory
RUN mkdir -p /app/data

# Expose port
EXPOSE 8003

# Run the application
CMD ["./build/execution_engine"] 