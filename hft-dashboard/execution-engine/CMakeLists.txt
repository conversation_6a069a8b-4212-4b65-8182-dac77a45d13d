cmake_minimum_required(VERSION 3.10)
project(execution_engine)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)
pkg_check_modules(PQ REQUIRED libpq)
pkg_check_modules(HIREDIS REQUIRED hiredis)

# Include directories
include_directories(${PQ_INCLUDE_DIRS})
include_directories(${HIREDIS_INCLUDE_DIRS})

# Add executable
add_executable(execution_engine 
    src/main.cpp
    src/order_manager.cpp
    src/risk_manager.cpp
    src/database_manager.cpp
)

# Link libraries
target_link_libraries(execution_engine 
    ${PQ_LIBRARIES}
    ${HIREDIS_LIBRARIES}
    ssl
    crypto
    pthread
)

# Compiler flags
target_compile_options(execution_engine PRIVATE 
    ${PQ_CFLAGS_OTHER}
    ${HIREDIS_CFLAGS_OTHER}
    -O3
    -<PERSON>
    -<PERSON>x<PERSON>
) 