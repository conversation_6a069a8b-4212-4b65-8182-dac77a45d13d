#pragma once

#include <string>
#include <map>

struct Order;

class RiskManager {
public:
    RiskManager();
    ~RiskManager();
    
    bool check_order_validity(const Order& order);
    void set_daily_loss_limit(double limit);
    void set_position_limit(const std::string& symbol, int limit);
    double get_current_pnl();
    
private:
    double daily_loss_limit_;
    std::map<std::string, int> position_limits_;
    std::map<std::string, int> current_positions_;
    double current_pnl_;
}; 