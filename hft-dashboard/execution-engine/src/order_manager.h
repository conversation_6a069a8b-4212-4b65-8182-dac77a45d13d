#pragma once

#include <string>
#include <vector>
#include <memory>

class DatabaseManager;
class RiskManager;

struct Order {
    std::string id;
    std::string symbol;
    std::string side; // "buy" or "sell"
    int quantity;
    double price;
    std::string status; // "pending", "filled", "cancelled"
    std::string timestamp;
};

class OrderManager {
public:
    OrderManager(DatabaseManager* db_manager, RiskManager* risk_manager);
    ~OrderManager();
    
    void process_orders();
    bool place_order(const Order& order);
    bool cancel_order(const std::string& order_id);
    std::vector<Order> get_orders();
    
private:
    DatabaseManager* db_manager_;
    RiskManager* risk_manager_;
    std::vector<Order> pending_orders_;
}; 