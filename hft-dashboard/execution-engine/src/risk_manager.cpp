#include "risk_manager.h"
#include "order_manager.h"
#include <iostream>
#include <cmath>

RiskManager::RiskManager() 
    : daily_loss_limit_(50000.0), current_pnl_(0.0) {
    std::cout << "RiskManager initialized" << std::endl;
}

RiskManager::~RiskManager() {
    std::cout << "RiskManager shutdown" << std::endl;
}

bool RiskManager::check_order_validity(const Order& order) {
    // Check daily loss limit
    if (current_pnl_ <= -daily_loss_limit_) {
        std::cout << "Order rejected: Daily loss limit exceeded" << std::endl;
        return false;
    }
    
    // Check position limits
    auto it = position_limits_.find(order.symbol);
    if (it != position_limits_.end()) {
        int current_pos = current_positions_[order.symbol];
        int new_pos = current_pos;
        
        if (order.side == "buy") {
            new_pos += order.quantity;
        } else if (order.side == "sell") {
            new_pos -= order.quantity;
        }
        
        if (std::abs(new_pos) > it->second) {
            std::cout << "Order rejected: Position limit exceeded for " << order.symbol << std::endl;
            return false;
        }
    }
    
    // Check order size
    if (order.quantity > 10000) {
        std::cout << "Order rejected: Order size too large" << std::endl;
        return false;
    }
    
    return true;
}

void RiskManager::set_daily_loss_limit(double limit) {
    daily_loss_limit_ = limit;
    std::cout << "Daily loss limit set to: " << limit << std::endl;
}

void RiskManager::set_position_limit(const std::string& symbol, int limit) {
    position_limits_[symbol] = limit;
    std::cout << "Position limit set for " << symbol << ": " << limit << std::endl;
}

double RiskManager::get_current_pnl() {
    return current_pnl_;
} 