#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include "order_manager.h"
#include "risk_manager.h"
#include "database_manager.h"

int main() {
    std::cout << "Starting HFT Execution Engine..." << std::endl;
    
    try {
        // Initialize components
        DatabaseManager db_manager;
        RiskManager risk_manager;
        OrderManager order_manager(&db_manager, &risk_manager);
        
        std::cout << "Execution Engine initialized successfully" << std::endl;
        
        // Main loop
        while (true) {
            // Process orders
            order_manager.process_orders();
            
            // Sleep for a short interval
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 