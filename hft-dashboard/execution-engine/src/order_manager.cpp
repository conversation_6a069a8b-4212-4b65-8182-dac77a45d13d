#include "order_manager.h"
#include "database_manager.h"
#include "risk_manager.h"
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>

OrderManager::OrderManager(DatabaseManager* db_manager, RiskManager* risk_manager)
    : db_manager_(db_manager), risk_manager_(risk_manager) {
    std::cout << "OrderManager initialized" << std::endl;
}

OrderManager::~OrderManager() {
    std::cout << "OrderManager shutdown" << std::endl;
}

void OrderManager::process_orders() {
    // Process pending orders
    for (auto it = pending_orders_.begin(); it != pending_orders_.end();) {
        Order& order = *it;
        
        // Check if order should be executed
        if (risk_manager_->check_order_validity(order)) {
            // Execute order
            order.status = "filled";
            db_manager_->save_order(order);
            std::cout << "Order executed: " << order.id << std::endl;
            
            // Remove from pending orders
            it = pending_orders_.erase(it);
        } else {
            ++it;
        }
    }
}

bool OrderManager::place_order(const Order& order) {
    // Validate order
    if (order.quantity <= 0 || order.price <= 0) {
        std::cerr << "Invalid order parameters" << std::endl;
        return false;
    }
    
    // Check risk limits
    if (!risk_manager_->check_order_validity(order)) {
        std::cerr << "Order rejected by risk manager" << std::endl;
        return false;
    }
    
    // Add to pending orders
    pending_orders_.push_back(order);
    std::cout << "Order placed: " << order.id << std::endl;
    
    return true;
}

bool OrderManager::cancel_order(const std::string& order_id) {
    for (auto it = pending_orders_.begin(); it != pending_orders_.end(); ++it) {
        if (it->id == order_id) {
            it->status = "cancelled";
            db_manager_->save_order(*it);
            pending_orders_.erase(it);
            std::cout << "Order cancelled: " << order_id << std::endl;
            return true;
        }
    }
    
    std::cerr << "Order not found: " << order_id << std::endl;
    return false;
}

std::vector<Order> OrderManager::get_orders() {
    return pending_orders_;
} 