global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Backend API
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Strategy Engine
  - job_name: 'strategy-engine'
    static_configs:
      - targets: ['strategy-engine:8001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Market Data Handler
  - job_name: 'market-data-handler'
    static_configs:
      - targets: ['market-data-handler:8002']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Execution Engine
  - job_name: 'execution-engine'
    static_configs:
      - targets: ['execution-engine:8003']
    metrics_path: '/metrics'
    scrape_interval: 5s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  # ClickHouse
  - job_name: 'clickhouse'
    static_configs:
      - targets: ['clickhouse:8123']
    metrics_path: '/metrics' 