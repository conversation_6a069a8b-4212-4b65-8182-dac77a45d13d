# HFT Dashboard Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL (User Management)
POSTGRES_DB=hft_dashboard
POSTGRES_USER=hft_user
POSTGRES_PASSWORD=secure_password_123
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# TimescaleDB (Trade/Order History)
TIMESCALEDB_DB=hft_trades
TIMESCALEDB_USER=hft_user
TIMESCALEDB_PASSWORD=secure_password_123
TIMESCALEDB_HOST=timescaledb
TIMESCALEDB_PORT=5432

# ClickHouse (Market Data)
CLICKHOUSE_DB=hft_market_data
CLICKHOUSE_HOST=clickhouse
CLICKHOUSE_PORT=9000
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=

# Redis (Cache & Message Broker)
REDIS_URL=redis://redis:6379
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# =============================================================================
# ZERODHA KITECONNECT API
# =============================================================================

# Your Zerodha API credentials
KITE_API_KEY=your_kite_api_key_here
KITE_API_SECRET=your_kite_api_secret_here

# KiteConnect settings
KITE_LOGIN_URL=https://kite.trade/connect/login
KITE_REDIRECT_URL=http://localhost:8000/auth/kite/callback

# =============================================================================
# SECURITY & AUTHENTICATION
# =============================================================================

# JWT Configuration
JWT_SECRET_KEY=your_super_secret_jwt_key_change_this_in_production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Application Security
SECRET_KEY=your_super_secret_app_key_change_this_in_production
ALGORITHM=HS256

# Password Security
BCRYPT_ROUNDS=12
MIN_PASSWORD_LENGTH=8

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Risk Management
MAX_DAILY_LOSS=50000  # Maximum daily loss in INR
MAX_POSITION_SIZE=1000000  # Maximum position size in INR
MAX_ORDERS_PER_MINUTE=60  # Rate limiting for orders

# Trading Instruments (NIFTY & BANKNIFTY)
NIFTY_SYMBOL=NIFTY
BANKNIFTY_SYMBOL=BANKNIFTY
TRADING_EXCHANGE=NFO

# Market Data Settings
TICK_DATA_RETENTION_DAYS=30
HISTORICAL_DATA_RETENTION_DAYS=365

# =============================================================================
# SYSTEM PERFORMANCE
# =============================================================================

# Latency Targets
MAX_ORDER_LATENCY_MS=100
MAX_MARKET_DATA_LATENCY_MS=50

# Resource Limits
MAX_CONCURRENT_STRATEGIES=10
MAX_CONCURRENT_USERS=50

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Grafana
GRAFANA_PASSWORD=admin123
GRAFANA_ADMIN_EMAIL=<EMAIL>

# Prometheus
PROMETHEUS_RETENTION_HOURS=200
PROMETHEUS_SCRAPE_INTERVAL=15s

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=/app/logs/hft_dashboard.log
MAX_LOG_FILE_SIZE_MB=100
LOG_BACKUP_COUNT=5

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug Mode (set to False in production)
DEBUG=True
ENVIRONMENT=development

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
ALLOWED_CREDENTIALS=True

# API Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=60

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Email Service (for password reset, notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_USE_TLS=True

# SMS Service (optional, for alerts)
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=your_sms_api_secret

# =============================================================================
# BACKUP & STORAGE
# =============================================================================

# Data Backup
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=/app/backups

# File Storage
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=["csv", "json", "xlsx", "pdf"]

# =============================================================================
# NOTIFICATIONS
# =============================================================================

# Email Notifications
EMAIL_NOTIFICATIONS_ENABLED=True
DAILY_REPORT_EMAIL=True
ALERT_EMAIL_ENABLED=True

# Webhook Notifications
WEBHOOK_URL=
WEBHOOK_SECRET=

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Database Connection Pool
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Redis Connection Pool
REDIS_POOL_SIZE=10
REDIS_MAX_CONNECTIONS=50

# WebSocket Settings
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

# Strategy Engine
STRATEGY_ENGINE_TIMEOUT=300
STRATEGY_MAX_MEMORY_MB=512

# Market Data Handler
MARKET_DATA_BATCH_SIZE=1000
MARKET_DATA_FLUSH_INTERVAL=1

# Execution Engine
EXECUTION_ENGINE_TIMEOUT=60
EXECUTION_MAX_RETRIES=3 