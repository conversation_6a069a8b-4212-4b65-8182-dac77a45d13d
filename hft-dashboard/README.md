# HFT Dashboard - Hybrid High-Frequency Trading System

A modern, user-friendly web application for managing a hybrid HFT system with Python strategy engine and C++ execution engine, specifically designed for Indian derivatives market (NIFTY & BANKNIFTY).

## 🚀 Features

### Core Functionality
- **Real-time Dashboard** - Live P&L, positions, and system metrics
- **Strategy Management** - Deploy, monitor, and backtest trading strategies
- **User Management** - Secure authentication with role-based access
- **Market Data Visualization** - Real-time charts and analytics
- **Order Management** - View and manage active orders
- **System Monitoring** - Performance metrics and health monitoring

### Technical Stack
- **Frontend**: React 18 + TypeScript + Tailwind CSS
- **Backend**: FastAPI + Python 3.11
- **Database**: PostgreSQL + Redis + ClickHouse + TimescaleDB
- **Message Broker**: Redis Pub/Sub + ZeroMQ
- **Monitoring**: Prometheus + Grafana
- **Deployment**: Docker Compose

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend│    │   FastAPI Backend│    │   Strategy Engine│
│   (Dashboard)   │◄──►│   (User Mgmt)   │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Redis Cache   │    │   PostgreSQL    │    │   ClickHouse    │
│   (Session/IPC) │    │   (User Data)   │    │   (Market Data) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   TimescaleDB   │    │   Order Engine  │    │   KiteConnect   │
│   (Trades)      │    │   (C++)         │    │   (Market Feed) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

### Hardware Requirements
- **CPU**: 4 cores (Intel i5/Ryzen 5 or better)
- **RAM**: 16 GB
- **Storage**: 250 GB SSD
- **Network**: 100 Mbps broadband or better

### Software Requirements
- **Docker**: 24.x or later
- **Docker Compose**: 2.20+
- **Node.js**: 18.x or later (for development)
- **Python**: 3.11+ (for development)

## 🛠️ Quick Start

### 1. Clone and Setup
```bash
git clone <repository-url>
cd hft-dashboard
```

### 2. Environment Configuration
```bash
# Copy environment files
cp .env.example .env
cp frontend/.env.example frontend/.env

# Edit configuration
nano .env
nano frontend/.env
```

### 3. Start the System
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f
```

### 4. Access the Dashboard
- **Dashboard**: http://localhost:3000
- **API Docs**: http://localhost:8000/docs
- **Grafana**: http://localhost:3001
- **Prometheus**: http://localhost:9090

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
# Database
POSTGRES_DB=hft_dashboard
POSTGRES_USER=hft_user
POSTGRES_PASSWORD=secure_password
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis
REDIS_URL=redis://redis:6379

# ClickHouse
CLICKHOUSE_HOST=clickhouse
CLICKHOUSE_PORT=9000
CLICKHOUSE_DB=hft_market_data

# KiteConnect API
KITE_API_KEY=your_api_key
KITE_API_SECRET=your_api_secret

# JWT
JWT_SECRET_KEY=your_jwt_secret
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Security
SECRET_KEY=your_secret_key
ALGORITHM=HS256
```

#### Frontend (frontend/.env)
```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws
REACT_APP_GRAFANA_URL=http://localhost:3001
```

## 📊 Dashboard Features

### 1. Real-time Dashboard
- Live P&L tracking
- Current positions
- System health indicators
- Recent trades
- Market data overview

### 2. Strategy Management
- Strategy deployment interface
- Performance metrics
- Backtesting results
- Strategy configuration
- Risk management settings

### 3. User Management
- User registration/login
- Password reset
- Account management
- Role-based access control
- Daily loss limits

### 4. Market Data
- Real-time price charts
- Volume analysis
- Technical indicators
- Historical data viewer
- Market depth

### 5. Order Management
- Active orders view
- Order history
- Fill reports
- Order modification
- Risk controls

### 6. System Monitoring
- Performance metrics
- Latency monitoring
- Error tracking
- Resource utilization
- Alert management

## 🔒 Security Features

- **JWT Authentication** - Secure token-based auth
- **Password Hashing** - bcrypt encryption
- **CORS Protection** - Cross-origin security
- **Rate Limiting** - API protection
- **Input Validation** - XSS/SQL injection prevention
- **HTTPS Support** - Encrypted communication

## 📈 Performance Optimization

- **Redis Caching** - Fast data access
- **Database Indexing** - Optimized queries
- **Connection Pooling** - Efficient DB connections
- **CDN Integration** - Static asset delivery
- **Lazy Loading** - Reduced initial load time

## 🚀 Deployment

### Production Deployment
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
```

### Development
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Run tests
docker-compose -f docker-compose.dev.yml run --rm backend pytest
docker-compose -f docker-compose.dev.yml run --rm frontend npm test
```

## 📝 API Documentation

The API documentation is available at:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the troubleshooting guide

## 🔄 Updates

Stay updated with the latest features and security patches by regularly pulling from the main branch.

---

**Note**: This system is designed for educational and research purposes. Please ensure compliance with local regulations before using for live trading. 