version: '3.8'

services:
  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000/ws
      - REACT_APP_GRAFANA_URL=http://localhost:3001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - hft-network

  # Backend FastAPI Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-hft_dashboard}
      - POSTGRES_USER=${POSTGRES_USER:-hft_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - REDIS_URL=redis://redis:6379
      - CLICKHOUSE_HOST=clickhouse
      - CLICKHOUSE_PORT=9000
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-hft_market_data}
      - KITE_API_KEY=${KITE_API_KEY}
      - KITE_API_SECRET=${KITE_API_SECRET}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your_jwt_secret}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - SECRET_KEY=${SECRET_KEY:-your_secret_key}
      - ALGORITHM=${ALGORITHM:-HS256}
    volumes:
      - ./backend:/app
      - ./data:/app/data
    depends_on:
      - postgres
      - redis
      - clickhouse
      - timescaledb
    networks:
      - hft-network

  # Strategy Engine (Python)
  strategy-engine:
    build:
      context: ./strategy-engine
      dockerfile: Dockerfile
    environment:
      - REDIS_URL=redis://redis:6379
      - CLICKHOUSE_HOST=clickhouse
      - CLICKHOUSE_PORT=9000
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-hft_market_data}
      - KITE_API_KEY=${KITE_API_KEY}
      - KITE_API_SECRET=${KITE_API_SECRET}
    volumes:
      - ./strategy-engine:/app
      - ./data:/app/data
    depends_on:
      - redis
      - clickhouse
      - market-data-handler
    networks:
      - hft-network

  # Market Data Handler (Python + KiteConnect)
  market-data-handler:
    build:
      context: ./market-data-handler
      dockerfile: Dockerfile
    environment:
      - REDIS_URL=redis://redis:6379
      - CLICKHOUSE_HOST=clickhouse
      - CLICKHOUSE_PORT=9000
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-hft_market_data}
      - KITE_API_KEY=${KITE_API_KEY}
      - KITE_API_SECRET=${KITE_API_SECRET}
    volumes:
      - ./market-data-handler:/app
      - ./data:/app/data
    depends_on:
      - redis
      - clickhouse
    networks:
      - hft-network

  # Order Execution Engine (C++)
  execution-engine:
    build:
      context: ./execution-engine
      dockerfile: Dockerfile
    environment:
      - REDIS_URL=redis://redis:6379
      - TIMESCALEDB_HOST=timescaledb
      - TIMESCALEDB_PORT=5432
      - TIMESCALEDB_DB=${TIMESCALEDB_DB:-hft_trades}
      - TIMESCALEDB_USER=${TIMESCALEDB_USER:-hft_user}
      - TIMESCALEDB_PASSWORD=${TIMESCALEDB_PASSWORD:-secure_password}
    volumes:
      - ./execution-engine:/app
      - ./data:/app/data
    depends_on:
      - redis
      - timescaledb
    networks:
      - hft-network

  # PostgreSQL Database (User Management + TimescaleDB)
  postgres:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-hft_dashboard}
      - POSTGRES_USER=${POSTGRES_USER:-hft_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - hft-network

  # Redis (Cache + Message Broker)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - hft-network

  # ClickHouse (Market Data Storage)
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    ports:
      - "8123:8123"
      - "9000:9000"
    environment:
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-hft_market_data}
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./clickhouse-config:/etc/clickhouse-server/config.d
    networks:
      - hft-network

  # TimescaleDB (Trade/Order History)
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=${TIMESCALEDB_DB:-hft_trades}
      - POSTGRES_USER=${TIMESCALEDB_USER:-hft_user}
      - POSTGRES_PASSWORD=${TIMESCALEDB_PASSWORD:-secure_password}
    ports:
      - "5433:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - hft-network

  # Prometheus (Metrics Collection)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - hft-network

  # Grafana (Dashboard)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - hft-network

  # Nginx (Reverse Proxy - Optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - hft-network

volumes:
  postgres_data:
  redis_data:
  clickhouse_data:
  timescaledb_data:
  prometheus_data:
  grafana_data:

networks:
  hft-network:
    driver: bridge 